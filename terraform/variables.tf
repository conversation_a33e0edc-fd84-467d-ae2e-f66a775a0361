/**
 * Terraform Variables for Billsnapp Infrastructure
 */

variable "project_id" {
  description = "The Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "The Google Cloud region for deployment"
  type        = string
  default     = "us-west1"
}

variable "artifact_registry_location" {
  description = "The location of the Artifact Registry"
  type        = string
  default     = "us"
}

variable "artifact_registry_repository" {
  description = "The name of the Artifact Registry repository"
  type        = string
  default     = "billsnapp"
}

variable "invoices_storage_bucket" {
  description = "The name of the GCS bucket for invoice storage"
  type        = string
  default     = "billsnapp-invoices"
}

variable "environment" {
  description = "The deployment environment (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "service_account_email" {
  description = "The email of the service account to use for Cloud Run services"
  type        = string
  default     = ""
}

variable "enable_gpu" {
  description = "Whether to enable GPU for OCR processing"
  type        = bool
  default     = true
}

# M2-04 Monitoring Variables
variable "slack_webhook_url" {
  description = "Slack webhook URL for M2-04 monitoring alerts"
  type        = string
  sensitive   = true
  default     = ""
}

variable "alert_email" {
  description = "Email address for M2-04 monitoring alerts"
  type        = string
  default     = ""
}

variable "billing_account_id" {
  description = "GCP Billing Account ID for cost monitoring"
  type        = string
  default     = ""
}
