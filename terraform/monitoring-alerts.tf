# M2-04 Monitoring and Alerting Infrastructure
# Creates Cloud Monitoring alerts for the 48-hour synthetic monitoring window

# Notification channel for Slack alerts
resource "google_monitoring_notification_channel" "slack_alerts" {
  display_name = "M2-04 Slack Alerts"
  type         = "slack"
  project      = var.project_id
  
  labels = {
    channel_name = "#alerts"
    url          = var.slack_webhook_url
  }
  
  description = "Slack notifications for M2-04 synthetic monitoring alerts"
}

# Notification channel for email alerts (backup)
resource "google_monitoring_notification_channel" "email_alerts" {
  display_name = "M2-04 Email Alerts"
  type         = "email"
  project      = var.project_id
  
  labels = {
    email_address = var.alert_email
  }
  
  description = "Email notifications for M2-04 synthetic monitoring alerts"
}

# Alert Policy: High Latency (p95 > 2s for 5 minutes)
resource "google_monitoring_alert_policy" "high_latency_alert" {
  display_name = "M2-04: High Latency Alert"
  project      = var.project_id
  
  documentation {
    content = <<-EOT
    This alert fires when the 95th percentile latency of the /ocr/start endpoint 
    exceeds 2 seconds for 5 consecutive minutes during the M2-04 monitoring window.
    
    This indicates potential performance issues with the TypeScript backend that 
    need investigation before proceeding to M2-05.
    EOT
  }
  
  conditions {
    display_name = "95th percentile latency > 2s"
    
    condition_threshold {
      filter = <<-EOT
      resource.type="cloud_run_revision"
      resource.labels.service_name="billsnapp-api"
      metric.type="run.googleapis.com/request_latencies"
      EOT
      
      aggregations {
        alignment_period   = "300s"  # 5 minutes
        per_series_aligner = "ALIGN_DELTA"
        cross_series_reducer = "REDUCE_PERCENTILE_95"
        group_by_fields = [
          "resource.labels.service_name"
        ]
      }
      
      comparison = "COMPARISON_GREATER_THAN"
      threshold_value = 2000  # 2 seconds in milliseconds
      duration = "300s"       # 5 minutes
      
      trigger {
        count = 1
      }
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.slack_alerts.name,
    google_monitoring_notification_channel.email_alerts.name
  ]
  
  alert_strategy {
    auto_close = "86400s"  # 24 hours
  }
  
  enabled = true
}

# Alert Policy: High Error Rate (5xx >= 2% over 5 minutes)
resource "google_monitoring_alert_policy" "high_error_rate_alert" {
  display_name = "M2-04: High Error Rate Alert"
  project      = var.project_id
  
  documentation {
    content = <<-EOT
    This alert fires when the 5xx error rate exceeds 2% over a 5-minute window
    during the M2-04 monitoring period.
    
    This indicates infrastructure or application issues that need immediate
    attention before proceeding to M2-05.
    EOT
  }
  
  conditions {
    display_name = "5xx error rate >= 2%"
    
    condition_threshold {
      filter = <<-EOT
      resource.type="cloud_run_revision"
      resource.labels.service_name="billsnapp-api"
      metric.type="run.googleapis.com/request_count"
      metric.labels.response_code_class="5xx"
      EOT
      
      aggregations {
        alignment_period   = "300s"  # 5 minutes
        per_series_aligner = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_SUM"
        group_by_fields = [
          "resource.labels.service_name"
        ]
      }
      
      comparison = "COMPARISON_GREATER_THAN"
      threshold_value = 0.02  # 2%
      duration = "300s"       # 5 minutes
      
      trigger {
        count = 1
      }
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.slack_alerts.name,
    google_monitoring_notification_channel.email_alerts.name
  ]
  
  alert_strategy {
    auto_close = "86400s"  # 24 hours
  }
  
  enabled = true
}

# Alert Policy: Vertex AI Spend Monitoring
resource "google_monitoring_alert_policy" "vertex_ai_spend_alert" {
  display_name = "M2-04: Vertex AI Daily Spend Alert"
  project      = var.project_id
  
  documentation {
    content = <<-EOT
    This alert fires when daily Vertex AI spending exceeds $20 during the 
    M2-04 monitoring window.
    
    This helps prevent unexpected costs during the synthetic load testing.
    EOT
  }
  
  conditions {
    display_name = "Daily Vertex AI spend > $20"
    
    condition_threshold {
      filter = <<-EOT
      resource.type="global"
      metric.type="billing.googleapis.com/billing/total_cost"
      metric.labels.service="Vertex AI"
      EOT
      
      aggregations {
        alignment_period   = "86400s"  # 24 hours
        per_series_aligner = "ALIGN_SUM"
        cross_series_reducer = "REDUCE_SUM"
      }
      
      comparison = "COMPARISON_GREATER_THAN"
      threshold_value = 20.0  # $20 USD
      duration = "0s"         # Immediate
      
      trigger {
        count = 1
      }
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.slack_alerts.name,
    google_monitoring_notification_channel.email_alerts.name
  ]
  
  alert_strategy {
    auto_close = "86400s"  # 24 hours
  }
  
  enabled = true
}

# Log-based alert for synthetic monitoring failures
resource "google_logging_metric" "synthetic_monitoring_failures" {
  name   = "synthetic_monitoring_failures"
  project = var.project_id
  
  filter = <<-EOT
  resource.type="global"
  logName="projects/${var.project_id}/logs/synthetic-monitoring-m2-04"
  (jsonPayload.health_check.status="FAILED" OR jsonPayload.ocr_test.status="FAILED")
  EOT
  
  metric_descriptor {
    metric_kind = "GAUGE"
    value_type  = "INT64"
    display_name = "Synthetic Monitoring Failures"
  }
  
  value_extractor = "EXTRACT(1)"
}

# Alert Policy: Synthetic Monitoring Failures
resource "google_monitoring_alert_policy" "synthetic_monitoring_failures_alert" {
  display_name = "M2-04: Synthetic Monitoring Failures"
  project      = var.project_id
  
  documentation {
    content = <<-EOT
    This alert fires when the synthetic monitoring detects failures in either
    the health check or OCR test endpoints.
    
    This indicates issues with the TypeScript backend that need immediate
    investigation during the M2-04 monitoring window.
    EOT
  }
  
  conditions {
    display_name = "Synthetic monitoring failure detected"
    
    condition_threshold {
      filter = "metric.type=\"logging.googleapis.com/user/synthetic_monitoring_failures\""
      
      aggregations {
        alignment_period   = "60s"   # 1 minute
        per_series_aligner = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_SUM"
      }
      
      comparison = "COMPARISON_GREATER_THAN"
      threshold_value = 0
      duration = "0s"  # Immediate
      
      trigger {
        count = 1
      }
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.slack_alerts.name,
    google_monitoring_notification_channel.email_alerts.name
  ]
  
  alert_strategy {
    auto_close = "3600s"  # 1 hour
  }
  
  enabled = true
}

# Dashboard for M2-04 monitoring
resource "google_monitoring_dashboard" "m2_04_monitoring" {
  dashboard_json = jsonencode({
    displayName = "M2-04: 48-Hour Synthetic Monitoring"
    
    gridLayout = {
      widgets = [
        {
          title = "API Response Times"
          xyChart = {
            dataSets = [
              {
                timeSeriesQuery = {
                  timeSeriesFilter = {
                    filter = "resource.type=\"cloud_run_revision\" resource.labels.service_name=\"billsnapp-api\" metric.type=\"run.googleapis.com/request_latencies\""
                    aggregation = {
                      alignmentPeriod = "60s"
                      perSeriesAligner = "ALIGN_DELTA"
                      crossSeriesReducer = "REDUCE_PERCENTILE_95"
                      groupByFields = ["resource.labels.service_name"]
                    }
                  }
                }
                plotType = "LINE"
                targetAxis = "Y1"
              }
            ]
            timeshiftDuration = "0s"
            yAxis = {
              label = "Latency (ms)"
              scale = "LINEAR"
            }
          }
        },
        {
          title = "Error Rate"
          xyChart = {
            dataSets = [
              {
                timeSeriesQuery = {
                  timeSeriesFilter = {
                    filter = "resource.type=\"cloud_run_revision\" resource.labels.service_name=\"billsnapp-api\" metric.type=\"run.googleapis.com/request_count\" metric.labels.response_code_class=\"5xx\""
                    aggregation = {
                      alignmentPeriod = "60s"
                      perSeriesAligner = "ALIGN_RATE"
                      crossSeriesReducer = "REDUCE_SUM"
                      groupByFields = ["resource.labels.service_name"]
                    }
                  }
                }
                plotType = "LINE"
                targetAxis = "Y1"
              }
            ]
            timeshiftDuration = "0s"
            yAxis = {
              label = "Error Rate"
              scale = "LINEAR"
            }
          }
        },
        {
          title = "Synthetic Monitoring Status"
          scorecard = {
            timeSeriesQuery = {
              timeSeriesFilter = {
                filter = "metric.type=\"logging.googleapis.com/user/synthetic_monitoring_failures\""
                aggregation = {
                  alignmentPeriod = "300s"
                  perSeriesAligner = "ALIGN_SUM"
                  crossSeriesReducer = "REDUCE_SUM"
                }
              }
            }
            sparkChartView = {
              sparkChartType = "SPARK_LINE"
            }
          }
        }
      ]
    }
  })
  
  project = var.project_id
}
