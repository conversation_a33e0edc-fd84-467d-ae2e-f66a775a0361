#!/bin/bash

# Safe Cleanup Script for Billsnapp (M2-03)
# Removes only truly dead artifacts: <PERSON><PERSON><PERSON>, <PERSON><PERSON> configs, helper scripts
# PRESERVES backend/ core files for M2-05 full purge

set -e

# Script configuration
DRY_RUN=false
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--dry-run] [--help]"
            echo ""
            echo "Safe cleanup script for M2-03 - removes only dead artifacts"
            echo ""
            echo "Options:"
            echo "  --dry-run    Show what would be deleted without actually deleting"
            echo "  --help       Show this help message"
            echo ""
            echo "This script removes:"
            echo "  • backend/workers/ (Celery workers)"
            echo "  • backend/requirements.txt"
            echo "  • Redis service from docker-compose.yml"
            echo "  • Legacy helper scripts (update_db.py, generate_test_token.py, etc.)"
            echo "  • Old Mongo/Redis env vars from .env.example"
            echo ""
            echo "This script PRESERVES:"
            echo "  • backend/app.py and core orchestrator files"
            echo "  • All TypeScript services"
            echo "  • Current CI/CD pipeline"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

cd "$PROJECT_ROOT"

echo "🧹 Safe Cleanup Script for Billsnapp (M2-03)"
echo "=============================================="
echo ""
if [ "$DRY_RUN" = true ]; then
    echo "🔍 DRY RUN MODE - No files will be deleted"
else
    echo "⚠️  LIVE MODE - Files will be permanently deleted"
fi
echo ""

# Safety check: Abort if backend/app.py exists (core orchestrator)
if [ -f "backend/app.py" ] || [ -f "backend/main.py" ]; then
    echo "🛡️  Safety Check: Core backend orchestrator detected"
    echo "   Found: backend/app.py or backend/main.py"
    echo "   This indicates the backend/ core is still in use."
    echo ""
    echo "❌ ABORTING: This script only removes dead artifacts."
    echo "   Use this script only after the core orchestrator has been migrated to TypeScript."
    echo "   For full backend removal, wait for M2-05."
    exit 1
fi

echo "✅ Safety check passed - no core orchestrator files detected"
echo ""

# Confirm with user (unless dry run)
if [ "$DRY_RUN" = false ]; then
    echo "This script will remove dead artifacts (Celery, Redis configs, helper scripts)."
    echo "It will NOT remove the entire backend/ folder."
    echo ""
    read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Cleanup cancelled."
        exit 1
    fi
    echo ""
fi

# Function to safely remove file/directory
safe_remove() {
    local path="$1"
    local description="$2"

    if [ -e "$path" ]; then
        if [ "$DRY_RUN" = true ]; then
            echo "  [DRY RUN] Would remove: $path ($description)"
        else
            echo "  🗑️  Removing: $path ($description)"
            rm -rf "$path"
            echo "  ✅ Removed: $path"
        fi
    else
        echo "  ℹ️  Not found: $path ($description)"
    fi
}

# Phase 1: Remove Celery workers and Redis dependencies
echo "📁 Phase 1: Removing Celery workers and Redis dependencies..."
echo ""

safe_remove "backend/workers/" "Celery worker directory"
safe_remove "backend/requirements.txt" "Python dependencies file"

# Remove individual Celery/Redis related files if they exist
celery_files=(
    "backend/workers/celery_app.py"
    "backend/workers/tasks.py"
    "backend/workers/__init__.py"
)

for file in "${celery_files[@]}"; do
    safe_remove "$file" "Celery worker file"
done

echo ""

# Phase 2: Remove legacy helper scripts
echo "📄 Phase 2: Removing legacy helper scripts..."
echo ""

legacy_scripts=(
    "update_db.py"
    "generate_test_token.py"
    "create_superadmin.py"
    "init_db.py"
    "update_imports.py"
    "setup.cfg"
    "pyproject.toml"
    "uv.lock"
)

for script in "${legacy_scripts[@]}"; do
    safe_remove "$script" "Legacy helper script"
done

# Phase 3: Clean up Docker configuration (remove Redis service only)
echo "🐳 Phase 3: Cleaning Docker configuration..."
echo ""

if [ -f "docker-compose.yml" ]; then
    if [ "$DRY_RUN" = true ]; then
        echo "  [DRY RUN] Would update docker-compose.yml to remove Redis service"
        echo "  [DRY RUN] Would backup original to docker-compose.yml.backup"
    else
        echo "  📝 Backing up docker-compose.yml to docker-compose.yml.backup"
        cp docker-compose.yml docker-compose.yml.backup

        echo "  🔧 Removing Redis service and Celery worker from docker-compose.yml"
        # Create a temporary file with Redis and Celery services removed
        python3 -c "
import yaml
import sys

try:
    with open('docker-compose.yml', 'r') as f:
        compose = yaml.safe_load(f)

    # Remove Redis service
    if 'services' in compose and 'redis' in compose['services']:
        del compose['services']['redis']
        print('  ✅ Removed Redis service')

    # Remove Celery worker service
    if 'services' in compose and 'celery-worker' in compose['services']:
        del compose['services']['celery-worker']
        print('  ✅ Removed Celery worker service')

    # Remove Redis volume
    if 'volumes' in compose and 'redis-data' in compose['volumes']:
        del compose['volumes']['redis-data']
        print('  ✅ Removed Redis volume')

    # Update backend service to remove Redis dependencies
    if 'services' in compose and 'backend' in compose['services']:
        backend = compose['services']['backend']
        if 'depends_on' in backend and 'redis' in backend['depends_on']:
            backend['depends_on'].remove('redis')
        if 'environment' in backend:
            backend['environment'] = [env for env in backend['environment']
                                    if not any(redis_var in env for redis_var in ['REDIS_HOST', 'REDIS_PORT'])]

    with open('docker-compose.yml', 'w') as f:
        yaml.dump(compose, f, default_flow_style=False, sort_keys=False)

    print('  ✅ docker-compose.yml updated (backup saved)')

except Exception as e:
    print(f'  ⚠️  Could not update docker-compose.yml automatically: {e}')
    print('  ℹ️  Please manually remove Redis and Celery services')
"
    fi
else
    echo "  ℹ️  docker-compose.yml not found"
fi

echo ""

# Phase 4: Clean up environment variables documentation
echo "🔧 Phase 4: Cleaning environment variables..."
echo ""

if [ -f ".env.example" ]; then
    if [ "$DRY_RUN" = true ]; then
        echo "  [DRY RUN] Would update .env.example to remove MongoDB and Redis variables"
        echo "  [DRY RUN] Would backup original to .env.example.backup"
    else
        echo "  📝 Backing up .env.example to .env.example.backup"
        cp .env.example .env.example.backup

        echo "  🔧 Removing MongoDB and Redis variables from .env.example"
        # Remove MongoDB and Redis sections
        sed -i.tmp '/# MongoDB/,/^$/d' .env.example 2>/dev/null || true
        sed -i.tmp '/MONGO_URI/d' .env.example 2>/dev/null || true
        sed -i.tmp '/MONGO_DBNAME/d' .env.example 2>/dev/null || true
        sed -i.tmp '/REDIS_HOST/d' .env.example 2>/dev/null || true
        sed -i.tmp '/REDIS_PORT/d' .env.example 2>/dev/null || true
        sed -i.tmp '/REDIS_DB/d' .env.example 2>/dev/null || true
        rm .env.example.tmp 2>/dev/null || true
        echo "  ✅ .env.example cleaned (backup saved)"
    fi
else
    echo "  ℹ️  .env.example not found"
fi

echo ""

# Phase 5: Verification
echo "🔍 Phase 5: Running verification checks..."
echo ""

echo "  📦 Checking for remaining Celery/Redis references..."
if [ "$DRY_RUN" = false ]; then
    celery_redis_refs=$(find . -name "*.py" -o -name "*.ts" -o -name "*.js" | xargs grep -l "celery\|redis" 2>/dev/null | grep -v node_modules | grep -v .git | grep -v backup || true)

    if [ -z "$celery_redis_refs" ]; then
        echo "  ✅ No Celery/Redis references found in source code"
    else
        echo "  ⚠️  Found remaining Celery/Redis references:"
        echo "$celery_redis_refs"
    fi
else
    echo "  [DRY RUN] Would check for remaining Celery/Redis references"
fi

echo "  🔧 Testing TypeScript compilation..."
if command -v pnpm &> /dev/null; then
    if [ "$DRY_RUN" = false ]; then
        if pnpm type-check >/dev/null 2>&1; then
            echo "  ✅ TypeScript compilation successful"
        else
            echo "  ⚠️  TypeScript compilation issues found"
        fi
    else
        echo "  [DRY RUN] Would test TypeScript compilation"
    fi
else
    echo "  ℹ️  pnpm not found, skipping TypeScript check"
fi

echo ""

# Summary
echo "🎉 Safe cleanup completed!"
echo ""
echo "📋 Summary of changes:"
if [ "$DRY_RUN" = true ]; then
    echo "  [DRY RUN] Would remove:"
else
    echo "  • Removed dead artifacts:"
fi
echo "    - backend/workers/ (Celery workers)"
echo "    - backend/requirements.txt"
echo "    - Legacy helper scripts (update_db.py, generate_test_token.py, etc.)"
echo "    - Redis service from docker-compose.yml"
echo "    - MongoDB/Redis variables from .env.example"
echo ""
echo "📁 Files preserved:"
echo "  • backend/ core orchestrator files (for M2-05)"
echo "  • All TypeScript services and CI/CD"
echo "  • Current infrastructure (Terraform)"
echo ""

if [ "$DRY_RUN" = false ]; then
    echo "📁 Backup files created:"
    echo "  • docker-compose.yml.backup"
    echo "  • .env.example.backup"
    echo ""
    echo "🚀 Next steps:"
    echo "  1. Review the changes: git diff"
    echo "  2. Test the application: pnpm dev"
    echo "  3. Run tests: pnpm test"
    echo "  4. Commit changes: git add . && git commit -m 'M2-03: Remove dead artifacts (Celery, Redis configs, helper scripts)'"
else
    echo "🚀 To apply these changes:"
    echo "  1. Run without --dry-run: $0"
    echo "  2. Review and test the changes"
    echo "  3. Commit the changes"
fi

echo ""
echo "✅ Safe cleanup complete!"
