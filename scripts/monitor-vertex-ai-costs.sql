-- M2-04 Vertex AI Cost Monitoring Query
-- This BigQuery scheduled query monitors daily Vertex AI spending
-- and triggers alerts if costs exceed $20/day during the monitoring window

-- Create a scheduled query that runs every hour
-- Query Name: m2-04-vertex-ai-cost-monitor
-- Schedule: Every 1 hour
-- Destination: monitoring_alerts.vertex_ai_daily_costs

WITH daily_vertex_costs AS (
  SELECT
    DATE(usage_start_time) as usage_date,
    service.description as service_name,
    SUM(cost) as daily_cost_usd,
    SUM(usage.amount) as usage_amount,
    usage.unit as usage_unit,
    COUNT(*) as line_items
  FROM 
    `{PROJECT_ID}.billing_export.gcp_billing_export_v1_{BILLING_ACCOUNT_ID}`
  WHERE
    -- Focus on Vertex AI services
    service.description LIKE '%Vertex AI%'
    OR service.description LIKE '%AI Platform%'
    OR service.description LIKE '%Machine Learning%'
    
    -- Only look at recent data (last 7 days)
    AND DATE(usage_start_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
    
    -- Filter out zero-cost items
    AND cost > 0
  
  GROUP BY
    usage_date,
    service_name,
    usage_unit
),

current_day_costs AS (
  SELECT
    usage_date,
    SUM(daily_cost_usd) as total_daily_cost,
    ARRAY_AGG(
      STRUCT(
        service_name,
        daily_cost_usd,
        usage_amount,
        usage_unit
      ) 
      ORDER BY daily_cost_usd DESC
    ) as service_breakdown
  FROM daily_vertex_costs
  WHERE usage_date = CURRENT_DATE()
  GROUP BY usage_date
),

cost_trend AS (
  SELECT
    usage_date,
    SUM(daily_cost_usd) as total_daily_cost,
    LAG(SUM(daily_cost_usd)) OVER (ORDER BY usage_date) as previous_day_cost,
    AVG(SUM(daily_cost_usd)) OVER (
      ORDER BY usage_date 
      ROWS BETWEEN 6 PRECEDING AND 1 PRECEDING
    ) as avg_weekly_cost
  FROM daily_vertex_costs
  GROUP BY usage_date
  ORDER BY usage_date DESC
  LIMIT 7
)

SELECT
  -- Current monitoring status
  CURRENT_DATETIME() as check_timestamp,
  'M2-04-VERTEX-AI-COST-MONITOR' as alert_type,
  
  -- Current day costs
  cdc.usage_date,
  cdc.total_daily_cost,
  
  -- Alert conditions
  CASE 
    WHEN cdc.total_daily_cost > 20.0 THEN 'ALERT'
    WHEN cdc.total_daily_cost > 15.0 THEN 'WARNING'
    ELSE 'OK'
  END as cost_status,
  
  -- Cost breakdown
  cdc.service_breakdown,
  
  -- Trend analysis
  ct.previous_day_cost,
  ct.avg_weekly_cost,
  ROUND(
    ((cdc.total_daily_cost - ct.previous_day_cost) / NULLIF(ct.previous_day_cost, 0)) * 100, 
    2
  ) as day_over_day_change_percent,
  
  -- Projections
  ROUND(cdc.total_daily_cost * 30, 2) as projected_monthly_cost,
  
  -- Alert message
  CASE 
    WHEN cdc.total_daily_cost > 20.0 THEN 
      CONCAT(
        '🚨 VERTEX AI COST ALERT: Daily spend ($', 
        ROUND(cdc.total_daily_cost, 2), 
        ') exceeds $20 threshold during M2-04 monitoring window'
      )
    WHEN cdc.total_daily_cost > 15.0 THEN 
      CONCAT(
        '⚠️ VERTEX AI COST WARNING: Daily spend ($', 
        ROUND(cdc.total_daily_cost, 2), 
        ') approaching $20 threshold'
      )
    ELSE 
      CONCAT(
        '✅ VERTEX AI COSTS OK: Daily spend ($', 
        ROUND(cdc.total_daily_cost, 2), 
        ') within acceptable limits'
      )
  END as alert_message

FROM current_day_costs cdc
LEFT JOIN cost_trend ct ON cdc.usage_date = ct.usage_date

-- Only return results if there are costs to report
WHERE cdc.total_daily_cost > 0

-- Add metadata for monitoring
UNION ALL

SELECT
  CURRENT_DATETIME() as check_timestamp,
  'M2-04-COST-MONITOR-METADATA' as alert_type,
  CURRENT_DATE() as usage_date,
  0.0 as total_daily_cost,
  'METADATA' as cost_status,
  [STRUCT(
    'MONITORING_STATUS' as service_name,
    0.0 as daily_cost_usd,
    1.0 as usage_amount,
    'CHECK' as usage_unit
  )] as service_breakdown,
  0.0 as previous_day_cost,
  0.0 as avg_weekly_cost,
  0.0 as day_over_day_change_percent,
  0.0 as projected_monthly_cost,
  CONCAT(
    'M2-04 Cost monitoring active. Query executed at ', 
    FORMAT_DATETIME('%Y-%m-%d %H:%M:%S UTC', CURRENT_DATETIME())
  ) as alert_message

ORDER BY check_timestamp DESC;
