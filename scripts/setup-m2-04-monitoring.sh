#!/bin/bash

# M2-04 Monitoring Setup Script
# Sets up the 48-hour synthetic monitoring infrastructure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-""}
REGION=${REGION:-"us-central1"}
SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL:-""}
ALERT_EMAIL=${ALERT_EMAIL:-""}
BILLING_ACCOUNT_ID=${BILLING_ACCOUNT_ID:-""}

echo -e "${BLUE}🚀 Setting up M2-04 48-Hour Synthetic Monitoring${NC}"
echo "=================================================="
echo ""

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

if [ -z "$PROJECT_ID" ]; then
    echo -e "${RED}❌ GOOGLE_CLOUD_PROJECT environment variable not set${NC}"
    echo "Please set it with: export GOOGLE_CLOUD_PROJECT=your-project-id"
    exit 1
fi

if [ -z "$SLACK_WEBHOOK_URL" ]; then
    echo -e "${YELLOW}⚠️  SLACK_WEBHOOK_URL not set - Slack alerts will be disabled${NC}"
fi

if [ -z "$ALERT_EMAIL" ]; then
    echo -e "${YELLOW}⚠️  ALERT_EMAIL not set - Email alerts will be disabled${NC}"
fi

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null; then
    echo -e "${RED}❌ Not authenticated with gcloud${NC}"
    echo "Please run: gcloud auth login"
    exit 1
fi

# Set the project
gcloud config set project $PROJECT_ID

echo -e "${GREEN}✅ Prerequisites checked${NC}"
echo ""

# Enable required APIs
echo -e "${YELLOW}🔧 Enabling required APIs...${NC}"

REQUIRED_APIS=(
    "monitoring.googleapis.com"
    "logging.googleapis.com"
    "bigquery.googleapis.com"
    "cloudfunctions.googleapis.com"
    "cloudscheduler.googleapis.com"
    "pubsub.googleapis.com"
    "cloudbilling.googleapis.com"
)

for api in "${REQUIRED_APIS[@]}"; do
    echo "Enabling $api..."
    gcloud services enable $api --quiet
done

echo -e "${GREEN}✅ APIs enabled${NC}"
echo ""

# Create BigQuery dataset for monitoring
echo -e "${YELLOW}📊 Setting up BigQuery monitoring dataset...${NC}"

DATASET_NAME="monitoring_alerts"

if ! bq ls -d $PROJECT_ID:$DATASET_NAME > /dev/null 2>&1; then
    echo "Creating BigQuery dataset: $DATASET_NAME"
    bq mk --dataset \
        --description="M2-04 monitoring and alerting dataset" \
        --location=US \
        $PROJECT_ID:$DATASET_NAME
else
    echo "BigQuery dataset $DATASET_NAME already exists"
fi

# Create table for cost monitoring results
echo "Creating cost monitoring table..."
bq mk --table \
    --description="Vertex AI daily cost monitoring results" \
    --time_partitioning_field=check_timestamp \
    --time_partitioning_type=DAY \
    $PROJECT_ID:$DATASET_NAME.vertex_ai_daily_costs \
    check_timestamp:DATETIME,alert_type:STRING,usage_date:DATE,total_daily_cost:FLOAT,cost_status:STRING,alert_message:STRING

echo -e "${GREEN}✅ BigQuery setup complete${NC}"
echo ""

# Deploy cost monitoring Cloud Function
echo -e "${YELLOW}☁️  Deploying cost monitoring Cloud Function...${NC}"

cd scripts/cost-alert-processor

# Install dependencies
npm install

# Deploy the function
gcloud functions deploy m2-04-cost-alert-processor \
    --runtime nodejs18 \
    --trigger-http \
    --allow-unauthenticated \
    --entry-point processCostAlerts \
    --memory 256MB \
    --timeout 540s \
    --set-env-vars "SLACK_WEBHOOK_URL=$SLACK_WEBHOOK_URL,ALERT_TOPIC=m2-04-cost-alerts" \
    --region $REGION

cd ../..

echo -e "${GREEN}✅ Cloud Function deployed${NC}"
echo ""

# Create Pub/Sub topic for alerts
echo -e "${YELLOW}📢 Setting up Pub/Sub topics...${NC}"

ALERT_TOPIC="m2-04-cost-alerts"

if ! gcloud pubsub topics describe $ALERT_TOPIC > /dev/null 2>&1; then
    echo "Creating Pub/Sub topic: $ALERT_TOPIC"
    gcloud pubsub topics create $ALERT_TOPIC
else
    echo "Pub/Sub topic $ALERT_TOPIC already exists"
fi

echo -e "${GREEN}✅ Pub/Sub setup complete${NC}"
echo ""

# Create Cloud Scheduler job for cost monitoring
echo -e "${YELLOW}⏰ Setting up Cloud Scheduler for cost monitoring...${NC}"

SCHEDULER_JOB="m2-04-cost-monitor"
FUNCTION_URL="https://$REGION-$PROJECT_ID.cloudfunctions.net/m2-04-cost-alert-processor"

# Delete existing job if it exists
if gcloud scheduler jobs describe $SCHEDULER_JOB --location=$REGION > /dev/null 2>&1; then
    echo "Deleting existing scheduler job..."
    gcloud scheduler jobs delete $SCHEDULER_JOB --location=$REGION --quiet
fi

echo "Creating Cloud Scheduler job for cost monitoring..."
gcloud scheduler jobs create http $SCHEDULER_JOB \
    --location=$REGION \
    --schedule="0 * * * *" \
    --uri=$FUNCTION_URL \
    --http-method=POST \
    --headers="Content-Type=application/json" \
    --message-body='{"trigger":"scheduled"}' \
    --description="M2-04 hourly Vertex AI cost monitoring"

echo -e "${GREEN}✅ Cloud Scheduler setup complete${NC}"
echo ""

# Apply Terraform monitoring infrastructure
echo -e "${YELLOW}🏗️  Deploying monitoring infrastructure with Terraform...${NC}"

cd terraform

# Initialize Terraform if needed
if [ ! -d ".terraform" ]; then
    terraform init
fi

# Plan the monitoring infrastructure
echo "Planning Terraform changes..."
terraform plan \
    -var="project_id=$PROJECT_ID" \
    -var="region=$REGION" \
    -var="slack_webhook_url=$SLACK_WEBHOOK_URL" \
    -var="alert_email=$ALERT_EMAIL" \
    -var="billing_account_id=$BILLING_ACCOUNT_ID" \
    -target=google_monitoring_notification_channel.slack_alerts \
    -target=google_monitoring_notification_channel.email_alerts \
    -target=google_monitoring_alert_policy.high_latency_alert \
    -target=google_monitoring_alert_policy.high_error_rate_alert \
    -target=google_monitoring_alert_policy.vertex_ai_spend_alert \
    -target=google_monitoring_alert_policy.synthetic_monitoring_failures_alert \
    -target=google_monitoring_dashboard.m2_04_monitoring

# Apply the changes
echo "Applying Terraform changes..."
terraform apply -auto-approve \
    -var="project_id=$PROJECT_ID" \
    -var="region=$REGION" \
    -var="slack_webhook_url=$SLACK_WEBHOOK_URL" \
    -var="alert_email=$ALERT_EMAIL" \
    -var="billing_account_id=$BILLING_ACCOUNT_ID" \
    -target=google_monitoring_notification_channel.slack_alerts \
    -target=google_monitoring_notification_channel.email_alerts \
    -target=google_monitoring_alert_policy.high_latency_alert \
    -target=google_monitoring_alert_policy.high_error_rate_alert \
    -target=google_monitoring_alert_policy.vertex_ai_spend_alert \
    -target=google_monitoring_alert_policy.synthetic_monitoring_failures_alert \
    -target=google_monitoring_dashboard.m2_04_monitoring

cd ..

echo -e "${GREEN}✅ Terraform monitoring infrastructure deployed${NC}"
echo ""

# Create GitHub repository secret for monitoring start time
echo -e "${YELLOW}🔐 Setting up GitHub secrets...${NC}"

echo "To complete the setup, please add the following secrets to your GitHub repository:"
echo ""
echo "Repository Settings > Secrets and variables > Actions > New repository secret:"
echo ""
echo "1. M2_04_START_TIME: (leave empty - will be set automatically when monitoring starts)"
echo "2. PRODUCTION_API_URL: https://your-api-url.a.run.app"
echo "3. SLACK_WEBHOOK_URL: $SLACK_WEBHOOK_URL"
echo ""

# Summary
echo -e "${GREEN}🎉 M2-04 Monitoring Setup Complete!${NC}"
echo "=================================="
echo ""
echo "✅ Infrastructure deployed:"
echo "   • BigQuery dataset and tables"
echo "   • Cloud Function for cost monitoring"
echo "   • Pub/Sub topics for alerts"
echo "   • Cloud Scheduler for hourly cost checks"
echo "   • Cloud Monitoring alerts and dashboard"
echo ""
echo "📋 Next steps:"
echo "   1. Add GitHub repository secrets (see above)"
echo "   2. Update the synthetic monitoring workflow with your API URL"
echo "   3. Enable the GitHub Action workflow"
echo "   4. Monitor the dashboard: https://console.cloud.google.com/monitoring/dashboards"
echo ""
echo "🚀 Ready to start the 48-hour monitoring window!"
echo ""
echo "To start monitoring, trigger the GitHub Action workflow:"
echo "   Repository > Actions > M2-04 Synthetic Monitoring > Run workflow"
