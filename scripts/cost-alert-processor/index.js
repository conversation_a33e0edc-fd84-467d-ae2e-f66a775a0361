/**
 * M2-04 Cost Alert Processor
 * Cloud Function that processes BigQuery cost monitoring results
 * and sends alerts when Vertex AI spending exceeds thresholds
 */

const { BigQuery } = require('@google-cloud/bigquery');
const { PubSub } = require('@google-cloud/pubsub');

const bigquery = new BigQuery();
const pubsub = new PubSub();

// Configuration
const PROJECT_ID = process.env.GOOGLE_CLOUD_PROJECT;
const SLACK_WEBHOOK_URL = process.env.SLACK_WEBHOOK_URL;
const ALERT_TOPIC = process.env.ALERT_TOPIC || 'm2-04-cost-alerts';

/**
 * HTTP Cloud Function entry point
 * Triggered by Cloud Scheduler every hour
 */
exports.processCostAlerts = async (req, res) => {
  try {
    console.log('Starting M2-04 cost monitoring check...');
    
    // Execute the cost monitoring query
    const costData = await runCostMonitoringQuery();
    
    if (!costData || costData.length === 0) {
      console.log('No cost data found for today');
      res.status(200).send('No cost data to process');
      return;
    }
    
    // Process each cost record
    for (const record of costData) {
      if (record.alert_type === 'M2-04-VERTEX-AI-COST-MONITOR') {
        await processCostAlert(record);
      }
    }
    
    console.log('Cost monitoring check completed successfully');
    res.status(200).send('Cost monitoring completed');
    
  } catch (error) {
    console.error('Error in cost monitoring:', error);
    
    // Send error alert
    await sendSlackAlert({
      type: 'ERROR',
      message: `🚨 M2-04 Cost Monitoring Error: ${error.message}`,
      details: {
        error: error.message,
        timestamp: new Date().toISOString()
      }
    });
    
    res.status(500).send(`Error: ${error.message}`);
  }
};

/**
 * Run the BigQuery cost monitoring query
 */
async function runCostMonitoringQuery() {
  const query = `
    WITH daily_vertex_costs AS (
      SELECT
        DATE(usage_start_time) as usage_date,
        service.description as service_name,
        SUM(cost) as daily_cost_usd
      FROM 
        \`${PROJECT_ID}.billing_export.gcp_billing_export_v1_*\`
      WHERE
        (service.description LIKE '%Vertex AI%'
         OR service.description LIKE '%AI Platform%'
         OR service.description LIKE '%Machine Learning%')
        AND DATE(usage_start_time) = CURRENT_DATE()
        AND cost > 0
      GROUP BY usage_date, service_name
    )
    
    SELECT
      CURRENT_DATETIME() as check_timestamp,
      'M2-04-VERTEX-AI-COST-MONITOR' as alert_type,
      CURRENT_DATE() as usage_date,
      COALESCE(SUM(daily_cost_usd), 0) as total_daily_cost,
      CASE 
        WHEN COALESCE(SUM(daily_cost_usd), 0) > 20.0 THEN 'ALERT'
        WHEN COALESCE(SUM(daily_cost_usd), 0) > 15.0 THEN 'WARNING'
        ELSE 'OK'
      END as cost_status,
      ARRAY_AGG(
        STRUCT(service_name, daily_cost_usd)
        ORDER BY daily_cost_usd DESC
      ) as service_breakdown
    FROM daily_vertex_costs
  `;
  
  const [rows] = await bigquery.query({
    query: query,
    location: 'US'
  });
  
  return rows;
}

/**
 * Process a cost alert record
 */
async function processCostAlert(record) {
  const { cost_status, total_daily_cost, alert_message, service_breakdown } = record;
  
  console.log(`Cost status: ${cost_status}, Daily cost: $${total_daily_cost}`);
  
  // Send alert for WARNING and ALERT statuses
  if (cost_status === 'ALERT' || cost_status === 'WARNING') {
    await sendSlackAlert({
      type: cost_status,
      message: alert_message,
      details: {
        daily_cost: total_daily_cost,
        threshold: cost_status === 'ALERT' ? 20.0 : 15.0,
        service_breakdown: service_breakdown,
        date: record.usage_date,
        timestamp: new Date().toISOString()
      }
    });
    
    // Publish to Pub/Sub for further processing
    await publishAlert({
      type: 'VERTEX_AI_COST',
      status: cost_status,
      cost: total_daily_cost,
      message: alert_message,
      timestamp: new Date().toISOString()
    });
  }
  
  // Log the status regardless
  console.log(`Vertex AI cost status: ${cost_status} - $${total_daily_cost}`);
}

/**
 * Send Slack alert
 */
async function sendSlackAlert({ type, message, details }) {
  if (!SLACK_WEBHOOK_URL) {
    console.log('No Slack webhook URL configured, skipping Slack alert');
    return;
  }
  
  const color = type === 'ALERT' ? '#ff0000' : type === 'WARNING' ? '#ffaa00' : '#00ff00';
  const emoji = type === 'ALERT' ? '🚨' : type === 'WARNING' ? '⚠️' : '✅';
  
  const payload = {
    text: `${emoji} M2-04 Cost Alert`,
    attachments: [
      {
        color: color,
        title: `${emoji} M2-04 Vertex AI Cost ${type}`,
        text: message,
        fields: [
          {
            title: 'Daily Cost',
            value: `$${details.daily_cost?.toFixed(2) || 'N/A'}`,
            short: true
          },
          {
            title: 'Threshold',
            value: `$${details.threshold?.toFixed(2) || 'N/A'}`,
            short: true
          },
          {
            title: 'Date',
            value: details.date || 'N/A',
            short: true
          },
          {
            title: 'Time',
            value: new Date(details.timestamp).toLocaleString(),
            short: true
          }
        ],
        footer: 'M2-04 Synthetic Monitoring',
        ts: Math.floor(Date.now() / 1000)
      }
    ]
  };
  
  // Add service breakdown if available
  if (details.service_breakdown && details.service_breakdown.length > 0) {
    const breakdown = details.service_breakdown
      .slice(0, 5) // Top 5 services
      .map(s => `• ${s.service_name}: $${s.daily_cost_usd?.toFixed(2) || '0.00'}`)
      .join('\n');
    
    payload.attachments[0].fields.push({
      title: 'Top Services',
      value: breakdown,
      short: false
    });
  }
  
  try {
    const fetch = require('node-fetch');
    const response = await fetch(SLACK_WEBHOOK_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      throw new Error(`Slack API error: ${response.status} ${response.statusText}`);
    }
    
    console.log('Slack alert sent successfully');
  } catch (error) {
    console.error('Failed to send Slack alert:', error);
  }
}

/**
 * Publish alert to Pub/Sub
 */
async function publishAlert(alertData) {
  try {
    const topic = pubsub.topic(ALERT_TOPIC);
    const messageBuffer = Buffer.from(JSON.stringify(alertData));
    
    await topic.publish(messageBuffer);
    console.log('Alert published to Pub/Sub successfully');
  } catch (error) {
    console.error('Failed to publish alert to Pub/Sub:', error);
  }
}

/**
 * Pub/Sub triggered function (alternative entry point)
 */
exports.processCostAlertsPubSub = async (message, context) => {
  try {
    console.log('Processing cost alert from Pub/Sub trigger');
    
    // Decode the message
    const data = message.data ? JSON.parse(Buffer.from(message.data, 'base64').toString()) : {};
    
    console.log('Received data:', data);
    
    // Process the alert
    await processCostAlert(data);
    
    console.log('Pub/Sub cost alert processing completed');
  } catch (error) {
    console.error('Error processing Pub/Sub cost alert:', error);
    throw error; // Re-throw to trigger retry
  }
};
