{"name": "m2-04-cost-alert-processor", "version": "1.0.0", "description": "Cloud Function for processing M2-04 Vertex AI cost alerts", "main": "index.js", "scripts": {"start": "functions-framework --target=processCostAlerts", "deploy": "gcloud functions deploy m2-04-cost-alert-processor --runtime nodejs18 --trigger-http --allow-unauthenticated --entry-point processCostAlerts", "deploy-pubsub": "gcloud functions deploy m2-04-cost-alert-processor-pubsub --runtime nodejs18 --trigger-topic m2-04-cost-alerts --entry-point processCostAlertsPubSub"}, "dependencies": {"@google-cloud/bigquery": "^7.3.0", "@google-cloud/pubsub": "^4.0.7", "node-fetch": "^2.7.0"}, "devDependencies": {"@google-cloud/functions-framework": "^3.3.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["google-cloud", "cloud-functions", "monitoring", "cost-alerts", "m2-04"], "author": "AiClearBill Team", "license": "MIT"}