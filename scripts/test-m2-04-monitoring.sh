#!/bin/bash

# M2-04 Monitoring Test Script
# Tests the monitoring infrastructure to ensure alerts fire correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-""}
API_URL=${API_URL:-"http://localhost:4000"}

echo -e "${BLUE}🧪 Testing M2-04 Monitoring Infrastructure${NC}"
echo "============================================="
echo ""

if [ -z "$PROJECT_ID" ]; then
    echo -e "${RED}❌ GOOGLE_CLOUD_PROJECT environment variable not set${NC}"
    exit 1
fi

echo -e "${YELLOW}📋 Test Configuration:${NC}"
echo "Project ID: $PROJECT_ID"
echo "API URL: $API_URL"
echo ""

# Test 1: Health endpoint latency test
echo -e "${YELLOW}🏥 Test 1: Health Endpoint Performance${NC}"
echo "Testing health endpoint latency and response..."

for i in {1..5}; do
    echo "Test $i/5:"
    
    START_TIME=$(date +%s%3N)
    RESPONSE=$(curl -s -w "%{http_code},%{time_total}" \
        --max-time 3 \
        --connect-timeout 2 \
        "$API_URL/health" || echo "000,999")
    END_TIME=$(date +%s%3N)
    
    HTTP_CODE=$(echo $RESPONSE | cut -d',' -f1)
    CURL_TIME=$(echo $RESPONSE | cut -d',' -f2)
    LATENCY_MS=$(echo "scale=0; $CURL_TIME * 1000" | bc)
    
    if [ "$HTTP_CODE" = "200" ] && (( $(echo "$LATENCY_MS < 2000" | bc -l) )); then
        echo -e "  ✅ HTTP $HTTP_CODE, ${LATENCY_MS}ms"
    elif [ "$HTTP_CODE" = "200" ]; then
        echo -e "  ⚠️  HTTP $HTTP_CODE, ${LATENCY_MS}ms (SLOW)"
    else
        echo -e "  ❌ HTTP $HTTP_CODE, ${LATENCY_MS}ms (ERROR)"
    fi
    
    sleep 1
done

echo ""

# Test 2: OCR endpoint infrastructure test
echo -e "${YELLOW}🔍 Test 2: OCR Endpoint Infrastructure${NC}"
echo "Testing OCR endpoint response (auth errors expected)..."

DUMMY_PAYLOAD='{
  "invoiceId": "test-monitoring-'$(date +%s)'",
  "tenantId": "test-tenant",
  "imageUrl": "https://example.com/test.jpg",
  "originalFilename": "test.jpg"
}'

RESPONSE=$(curl -s -w "%{http_code},%{time_total}" \
    --max-time 5 \
    --connect-timeout 2 \
    -X POST \
    -H "Content-Type: application/json" \
    -d "$DUMMY_PAYLOAD" \
    "$API_URL/trpc/ocr.startOcrJob" || echo "000,999")

HTTP_CODE=$(echo $RESPONSE | cut -d',' -f1)
CURL_TIME=$(echo $RESPONSE | cut -d',' -f2)
LATENCY_MS=$(echo "scale=0; $CURL_TIME * 1000" | bc)

echo "OCR endpoint test:"
if [ "$HTTP_CODE" -lt 500 ] && (( $(echo "$LATENCY_MS < 2000" | bc -l) )); then
    echo -e "  ✅ HTTP $HTTP_CODE, ${LATENCY_MS}ms (Infrastructure healthy)"
elif [ "$HTTP_CODE" -lt 500 ]; then
    echo -e "  ⚠️  HTTP $HTTP_CODE, ${LATENCY_MS}ms (SLOW but healthy)"
else
    echo -e "  ❌ HTTP $HTTP_CODE, ${LATENCY_MS}ms (Infrastructure issue)"
fi

echo ""

# Test 3: Cloud Logging integration
echo -e "${YELLOW}📊 Test 3: Cloud Logging Integration${NC}"
echo "Testing log entry creation..."

LOG_ENTRY='{
  "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'",
  "severity": "INFO",
  "labels": {
    "component": "monitoring-test",
    "milestone": "M2-04"
  },
  "jsonPayload": {
    "test_type": "infrastructure_validation",
    "health_check": {
      "status": "OK",
      "latency_ms": "'$LATENCY_MS'"
    },
    "message": "M2-04 monitoring infrastructure test"
  }
}'

if echo "$LOG_ENTRY" | gcloud logging write synthetic-monitoring-m2-04 --severity=INFO; then
    echo -e "  ✅ Log entry created successfully"
else
    echo -e "  ❌ Failed to create log entry"
fi

echo ""

# Test 4: BigQuery dataset access
echo -e "${YELLOW}📈 Test 4: BigQuery Monitoring Dataset${NC}"
echo "Testing BigQuery dataset access..."

if bq ls -d $PROJECT_ID:monitoring_alerts > /dev/null 2>&1; then
    echo -e "  ✅ BigQuery dataset accessible"
    
    # Test table access
    if bq show $PROJECT_ID:monitoring_alerts.vertex_ai_daily_costs > /dev/null 2>&1; then
        echo -e "  ✅ Cost monitoring table accessible"
    else
        echo -e "  ⚠️  Cost monitoring table not found (may need setup)"
    fi
else
    echo -e "  ❌ BigQuery dataset not accessible"
fi

echo ""

# Test 5: Cloud Function availability
echo -e "${YELLOW}☁️  Test 5: Cloud Function Deployment${NC}"
echo "Testing cost monitoring Cloud Function..."

FUNCTION_URL="https://us-central1-$PROJECT_ID.cloudfunctions.net/m2-04-cost-alert-processor"

FUNCTION_RESPONSE=$(curl -s -w "%{http_code}" \
    --max-time 10 \
    --connect-timeout 5 \
    -X POST \
    -H "Content-Type: application/json" \
    -d '{"trigger":"test"}' \
    "$FUNCTION_URL" || echo "000")

if [ "$FUNCTION_RESPONSE" = "200" ]; then
    echo -e "  ✅ Cloud Function responding"
elif [ "$FUNCTION_RESPONSE" = "404" ]; then
    echo -e "  ⚠️  Cloud Function not deployed (run setup script)"
else
    echo -e "  ❌ Cloud Function error: HTTP $FUNCTION_RESPONSE"
fi

echo ""

# Test 6: Monitoring alerts configuration
echo -e "${YELLOW}🚨 Test 6: Monitoring Alerts Configuration${NC}"
echo "Checking Cloud Monitoring alert policies..."

ALERT_POLICIES=$(gcloud alpha monitoring policies list --filter="displayName:M2-04" --format="value(name)" 2>/dev/null | wc -l)

if [ "$ALERT_POLICIES" -gt 0 ]; then
    echo -e "  ✅ Found $ALERT_POLICIES M2-04 alert policies"
    
    # List the policies
    echo "  Alert policies:"
    gcloud alpha monitoring policies list --filter="displayName:M2-04" --format="table(displayName,enabled)" 2>/dev/null | sed 's/^/    /'
else
    echo -e "  ⚠️  No M2-04 alert policies found (run Terraform setup)"
fi

echo ""

# Test 7: GitHub Action workflow validation
echo -e "${YELLOW}⚙️  Test 7: GitHub Action Workflow${NC}"
echo "Validating synthetic monitoring workflow..."

if [ -f ".github/workflows/synthetic-monitoring.yml" ]; then
    echo -e "  ✅ Synthetic monitoring workflow file exists"
    
    # Check for required secrets
    echo "  Required GitHub secrets:"
    echo "    • M2_04_START_TIME (auto-managed)"
    echo "    • PRODUCTION_API_URL"
    echo "    • SLACK_WEBHOOK_URL"
    echo "    • GCP_SA_KEY"
    echo "    • GCP_PROJECT_ID"
    echo ""
    echo "  💡 Add these secrets in: Repository > Settings > Secrets and variables > Actions"
else
    echo -e "  ❌ Synthetic monitoring workflow file not found"
fi

echo ""

# Summary
echo -e "${GREEN}📋 Test Summary${NC}"
echo "==============="
echo ""
echo "✅ Tests completed. Review the results above."
echo ""
echo "🚀 Next steps to start M2-04 monitoring:"
echo "   1. Ensure all tests show ✅ (run setup script if needed)"
echo "   2. Add required GitHub repository secrets"
echo "   3. Update API_URL in the GitHub workflow"
echo "   4. Enable and trigger the GitHub Action workflow"
echo ""
echo "📊 Monitor progress at:"
echo "   • GitHub Actions: https://github.com/Jpkay/AiClearBill/actions"
echo "   • Cloud Monitoring: https://console.cloud.google.com/monitoring"
echo "   • Cloud Logging: https://console.cloud.google.com/logs"
echo ""
echo -e "${BLUE}🎯 Ready for 48-hour monitoring window!${NC}"
