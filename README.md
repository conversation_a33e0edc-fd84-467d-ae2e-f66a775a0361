# AiClearBill (Billsnapp) &nbsp;🧾⚡

Production-ready, multi-tenant invoice-automation platform that combines **tiered AI-powered OCR**, **robust accounting connectors**, and **enterprise-grade RBAC** to eliminate manual data-entry and streamline bookkeeping.

---

## 1 · Why AiClearBill?

| Capability | Description |
| ---------- | ----------- |
| Tiered OCR Pipeline | Three-layer extraction (Gemma → Gemini → Document AI) delivers 99 %+ field accuracy while controlling costs. |
| Accounting Integrations | Out-of-the-box QuickBooks Online connector with secure token storage, bulk push, webhooks, and delta sync. |
| Multi-Tenant & RBAC | Fine-grained roles (`<PERSON><PERSON><PERSON>`, `Checker`, `Builder-in-Chief`, …) and Firestore security rules protect every tenant. |
| Offline-First PWA | React + Vite frontend works on desktop & mobile (installable PWA, IndexedDB caching, service-worker sync). |
| Cloud-Native | Firebase Auth, Firestore, MongoDB Atlas, Google Cloud Run, Pub/Sub, Cloud Tasks, KMS encryption. |

---

## 2 · Quick Start (Developer)

```bash
# 1. <PERSON><PERSON> & enter the repo
git clone https://github.com/Jpkay/AiClearBill.git
cd AiClearBill

# 2. One-shot environment bootstrap
./setup.sh          # installs pnpm deps, builds shared pkgs, creates .env files…

# 3. Launch all apps in watch mode
pnpm dev            # turbo runs apps/api and apps/web concurrently

# 4. Run tests & type-checks
pnpm test
pnpm lint
pnpm type-check
```

- 📖 **Development Guide**: For detailed setup instructions, debugging tips, and troubleshooting → [docs/DEV_GUIDE.md](docs/DEV_GUIDE.md)

> **Prerequisites**  
> Node 18+, pnpm 8+, Docker (for optional `docker-compose up`), and a Firebase project for local emulators.

---

## 3 · API Documentation  🚀
*(section content unchanged)*

---

## 4 · Monorepo Layout

```
.
├── apps/
│   ├── api/        # TypeScript tRPC API  ➜  see apps/api/README.md
│   └── web/        # React PWA frontend  ➜  see apps/web/README.md
├── backend/        # Legacy Python FastAPI + Celery workers
…
```

---

## 5 · Architecture

### 5.1 Current Stack Overview
AiClearBill currently operates a **dual-backend** architecture:

| Layer | Tech | Purpose |
|-------|------|---------|
| **TypeScript API** (`apps/api`) | Fastify + tRPC | Authentication, RBAC, Firestore ops, QuickBooks integration, *new* OCR orchestrator |
| **Python OCR Service** (`backend/`) | FastAPI | Legacy OCR orchestration (Gemini/Tesseract) and a handful of endpoints |

Both containers run on Cloud Run and share Pub/Sub topics, leading to duplicated logging, error handling, and Firestore writes.

### 5.2 Migration Strategy
We are converging on a **single TypeScript backend**.  
See the full phased plan, diagrams, risks, and acceptance criteria in  
➡️ **[docs/DUAL_BACKEND_MIGRATION.md](docs/DUAL_BACKEND_MIGRATION.md)**.

*(All subsequent sections have been renumbered accordingly.)*

---

## 6 · Monitoring

### M2-04: 48-Hour Synthetic Monitoring

AiClearBill includes comprehensive monitoring for the TypeScript backend during the M2-04 migration phase. This ensures the system runs error-free for 48 hours before final Python cleanup.

#### 🔍 **Synthetic Monitoring**

Automated tests run every minute to verify:
- **Health endpoint** (`/health`) responds within 2 seconds
- **OCR endpoint** (`/trpc/ocr.startOcrJob`) infrastructure is healthy
- **Error rates** stay below 2% (5xx responses)
- **Latency** p95 stays below 2 seconds

#### 📊 **Monitoring Dashboard**

View real-time metrics: [Cloud Monitoring Dashboard](https://console.cloud.google.com/monitoring/dashboards)

Key metrics tracked:
- API response times (p50, p95, p99)
- Error rates by endpoint
- Vertex AI cost tracking
- Synthetic test success/failure rates

#### 🚨 **Alerts**

Alerts are configured for:

| Alert | Threshold | Action |
|-------|-----------|--------|
| **High Latency** | p95 > 2s for 5 min | Slack + Email |
| **Error Rate** | 5xx ≥ 2% for 5 min | Slack + Email |
| **Cost Overrun** | Vertex AI > $20/day | Slack + Email |
| **Synthetic Failures** | Any test failure | Immediate alert |

#### 🛠️ **Managing Alerts**

**Mute alerts temporarily:**
```bash
# Mute for 1 hour
gcloud alpha monitoring policies list --filter="displayName:M2-04"
gcloud alpha monitoring snoozes create --policy=POLICY_ID --duration=3600s
```

**View alert history:**
```bash
# Check recent alerts
gcloud logging read "resource.type=global AND logName=projects/PROJECT_ID/logs/monitoring.googleapis.com%2Falerts"
```

#### 🔄 **Rerun Monitoring Window**

To restart the 48-hour monitoring window:

1. **Reset the timer:**
   ```bash
   # Clear the start time secret
   gh secret delete M2_04_START_TIME
   ```

2. **Trigger the workflow:**
   ```bash
   # Via GitHub CLI
   gh workflow run "M2-04 Synthetic Monitoring"

   # Or via GitHub UI
   # Repository > Actions > M2-04 Synthetic Monitoring > Run workflow
   ```

3. **Monitor progress:**
   - Check [GitHub Actions](../../actions) for test results
   - View [Cloud Logging](https://console.cloud.google.com/logs) for detailed logs
   - Monitor [Dashboard](https://console.cloud.google.com/monitoring/dashboards) for metrics

#### 📋 **Setup Instructions**

To set up monitoring for a new environment:

```bash
# 1. Configure environment
export GOOGLE_CLOUD_PROJECT=your-project-id
export SLACK_WEBHOOK_URL=your-slack-webhook
export ALERT_EMAIL=<EMAIL>

# 2. Run setup script
./scripts/setup-m2-04-monitoring.sh

# 3. Add GitHub secrets (see script output)

# 4. Enable the workflow
# Repository > Actions > Enable workflow
```

#### 🎯 **Success Criteria**

The monitoring window completes successfully when:
- ✅ 48 hours of continuous green status
- ✅ No 5xx errors above 2% threshold
- ✅ No latency spikes above 2 seconds
- ✅ Vertex AI costs stay under $20/day
- ✅ All synthetic tests pass

Upon completion, you'll receive a Slack notification: **"M2-04 green—proceed to M2-05"**

---

## 7 · Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

---

## 8 · License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---
