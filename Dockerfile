# Billsnapp API Production Dockerfile
# M2-04-fix: Real API container for Cloud Run deployment

FROM node:18-slim

# Install required dependencies for Canvas and image processing
RUN apt-get update && apt-get install -y \
    build-essential \
    libcairo2-dev \
    libpango1.0-dev \
    libjpeg-dev \
    libgif-dev \
    librsvg2-dev \
    python3 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy workspace manifest and package files
COPY pnpm-workspace.yaml pnpm-lock.yaml package.json turbo.json ./

# Copy workspace packages and apps
COPY apps/ ./apps/
COPY packages/ ./packages/

# Install PNPM (compatible version)
RUN npm install -g pnpm@latest

# Install dependencies (use --force to handle version differences)
RUN pnpm install --force

# Build shared-types first, then API (ensures workspace dependencies are available)
RUN pnpm --filter @billsnapp/shared-types build
RUN pnpm --filter @billsnapp/api build

# Set environment variables for Cloud Run
ENV NODE_ENV=production
ENV PORT=8080
ENV HOST=0.0.0.0

# Expose the port
EXPOSE 8080

# Run the main API server
CMD ["node", "apps/api/dist/index.js"]
