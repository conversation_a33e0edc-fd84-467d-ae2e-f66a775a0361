# LEGACY PYTHON BACKEND DOCKERFILE - DEPRECATED
# This Dockerfile is no longer used after M2-04 migration to TypeScript backend
# Keeping for reference during M2-05 cleanup phase
# TODO: Remove this file completely in M2-05

# Minimal valid Dockerfile to prevent build errors (will be removed in M2-05)
FROM alpine:latest
RUN echo "This Dockerfile is deprecated. Use apps/api/Dockerfile.ocr-processor instead."
CMD ["echo", "Legacy Python backend deprecated. Use TypeScript API instead."]

# OLD – no longer needed after TypeScript migration
# FROM python:3.12-slim
#
# WORKDIR /app
#
# # Install system dependencies
# RUN apt-get update && apt-get install -y --no-install-recommends \
#     build-essential \
#     tesseract-ocr \
#     && apt-get clean \
#     && rm -rf /var/lib/apt/lists/*
#
# # Install Python dependencies
# COPY backend/requirements.txt .
# RUN pip install --no-cache-dir -r requirements.txt
#
# # Install additional dependencies for development
# RUN pip install --no-cache-dir pytest pytest-asyncio pytest-cov black flake8
#
# # Copy application code
# COPY backend/ ./backend/
# COPY pyproject.toml .
#
# # Set environment variables
# ENV PYTHONPATH=/app
# ENV PYTHONUNBUFFERED=1
#
# # Expose port
# EXPOSE 8000
#
# # Command to run the application
# CMD ["uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# ============================================================================
# MIGRATION NOTICE:
# The Python backend has been replaced by TypeScript API (apps/api/)
#
# For TypeScript API Docker builds, use:
# - apps/api/Dockerfile.ocr-processor (CPU version)
# - apps/api/Dockerfile.ocr-processor-gpu (GPU version)
#
# This file will be removed in M2-05 final cleanup
# ============================================================================
