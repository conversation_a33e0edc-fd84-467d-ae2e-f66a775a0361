

> @billsnapp/api@0.0.0 type-check /Users/<USER>/Documents/GitHub/AiClearBill/apps/api
> tsc --noEmit

[96msrc/auth/permissions.ts[0m:[93m8[0m:[93m10[0m - [91merror[0m[90m TS2305: [0mModule '"../trpc/context"' has no exported member 'UserInfo'.

[7m8[0m import { UserInfo } from '../trpc/context';
[7m [0m [91m         ~~~~~~~~[0m

[96msrc/config/firebase-test.ts[0m:[93m5[0m:[93m36[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ exists: boolean; data: Mock<UnknownFunction>; }' is not assignable to parameter of type 'never'.

[7m  5[0m   get: jest.fn().mockResolvedValue({
[7m   [0m [91m                                   ~[0m
[7m  6[0m     exists: true,
[7m   [0m [91m~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m 13[0m     }),
[7m   [0m [91m~~~~~~~[0m
[7m 14[0m   }),
[7m   [0m [91m~~~[0m

[96msrc/config/firebase-test.ts[0m:[93m15[0m:[93m36[0m - [91merror[0m[90m TS2345: [0mArgument of type '{}' is not assignable to parameter of type 'never'.

[7m15[0m   set: jest.fn().mockResolvedValue({}),
[7m  [0m [91m                                   ~~[0m

[96msrc/config/firebase-test.ts[0m:[93m16[0m:[93m39[0m - [91merror[0m[90m TS2345: [0mArgument of type '{}' is not assignable to parameter of type 'never'.

[7m16[0m   update: jest.fn().mockResolvedValue({}),
[7m  [0m [91m                                      ~~[0m

[96msrc/config/firebase-test.ts[0m:[93m25[0m:[93m36[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ empty: boolean; docs: { id: string; data: Mock<UnknownFunction>; }[]; }' is not assignable to parameter of type 'never'.

[7m 25[0m   get: jest.fn().mockResolvedValue({
[7m   [0m [91m                                   ~[0m
[7m 26[0m     empty: false,
[7m   [0m [91m~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m 33[0m     }],
[7m   [0m [91m~~~~~~~[0m
[7m 34[0m   }),
[7m   [0m [91m~~~[0m

[96msrc/config/firebase-test.ts[0m:[93m49[0m:[93m42[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ exists: boolean; data: Mock<UnknownFunction>; }' is not assignable to parameter of type 'never'.

[7m 49[0m         get: jest.fn().mockResolvedValue({
[7m   [0m [91m                                         ~[0m
[7m 50[0m           exists: true,
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m 54[0m           }),
[7m   [0m [91m~~~~~~~~~~~~~[0m
[7m 55[0m         }),
[7m   [0m [91m~~~~~~~~~[0m

[96msrc/config/firebase-test.ts[0m:[93m59[0m:[93m14[0m - [91merror[0m[90m TS18046: [0m'callback' is of type 'unknown'.

[7m59[0m       return callback(mockTransaction);
[7m  [0m [91m             ~~~~~~~~[0m

[96msrc/config/firebase-test.ts[0m:[93m65[0m:[93m43[0m - [91merror[0m[90m TS2345: [0mArgument of type 'never[]' is not assignable to parameter of type 'never'.

[7m65[0m       commit: jest.fn().mockResolvedValue([]),
[7m  [0m [91m                                          ~~[0m

[96msrc/config/firebase-test.ts[0m:[93m71[0m:[93m48[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ uid: string; email: string; }' is not assignable to parameter of type 'never'.

[7m71[0m     verifyIdToken: jest.fn().mockResolvedValue({
[7m  [0m [91m                                               ~[0m
[7m72[0m       uid: 'test-user-id',
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m73[0m       email: '<EMAIL>',
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m74[0m     }),
[7m  [0m [91m~~~~~[0m

[96msrc/config/firebase-test.ts[0m:[93m75[0m:[93m42[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ uid: string; email: string; displayName: string; }' is not assignable to parameter of type 'never'.

[7m 75[0m     getUser: jest.fn().mockResolvedValue({
[7m   [0m [91m                                         ~[0m
[7m 76[0m       uid: 'test-user-id',
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m 78[0m       displayName: 'Test User',
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m 79[0m     }),
[7m   [0m [91m~~~~~[0m

[96msrc/config/firebase-test.ts[0m:[93m86[0m:[93m47[0m - [91merror[0m[90m TS2345: [0mArgument of type 'Buffer<ArrayBuffer>[]' is not assignable to parameter of type 'never'.

[7m86[0m         download: jest.fn().mockResolvedValue([Buffer.from('test-file-content')]),
[7m  [0m [91m                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/config/firebase-test.ts[0m:[93m87[0m:[93m51[0m - [91merror[0m[90m TS2345: [0mArgument of type 'string[]' is not assignable to parameter of type 'never'.

[7m87[0m         getSignedUrl: jest.fn().mockResolvedValue(['https://test-signed-url.com']),
[7m  [0m [91m                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/config/firebase.ts[0m:[93m38[0m:[93m21[0m - [91merror[0m[90m TS2379: [0mArgument of type '{ credential: Credential; storageBucket: string | undefined; }' is not assignable to parameter of type 'AppOptions' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'storageBucket' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.

[7m38[0m       initializeApp({
[7m  [0m [91m                    ~[0m
[7m39[0m         credential: cert(serviceAccount),
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m40[0m         storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m41[0m       });
[7m  [0m [91m~~~~~~~[0m

[96msrc/config/logger.ts[0m:[93m7[0m:[93m28[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.
  Overload 1 of 2, '(optionsOrStream?: DestinationStream | LoggerOptions<never> | undefined): Logger<never>', gave the following error.
    Argument of type '{ level: string; transport: { target: string; options: { colorize: boolean; translateTime: string; ignore: string; }; } | undefined; base: { severity: string; serviceContext: { service: string; version: string; }; } | undefined; }' is not assignable to parameter of type 'DestinationStream | LoggerOptions<never> | undefined'.
      Type '{ level: string; transport: { target: string; options: { colorize: boolean; translateTime: string; ignore: string; }; } | undefined; base: { severity: string; serviceContext: { service: string; version: string; }; } | undefined; }' is not assignable to type 'LoggerOptions<never>' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
        Types of property 'transport' are incompatible.
          Type '{ target: string; options: { colorize: boolean; translateTime: string; ignore: string; }; } | undefined' is not assignable to type 'TransportSingleOptions<Record<string, any>> | TransportMultiOptions<Record<string, any>> | TransportPipelineOptions<Record<string, any>>'.
            Type 'undefined' is not assignable to type 'TransportSingleOptions<Record<string, any>> | TransportMultiOptions<Record<string, any>> | TransportPipelineOptions<Record<string, any>>'.
  Overload 2 of 2, '(options: LoggerOptions<never>, stream?: DestinationStream | undefined): Logger<never>', gave the following error.
    Argument of type '{ level: string; transport: { target: string; options: { colorize: boolean; translateTime: string; ignore: string; }; } | undefined; base: { severity: string; serviceContext: { service: string; version: string; }; } | undefined; }' is not assignable to parameter of type 'LoggerOptions<never>' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'transport' are incompatible.
        Type '{ target: string; options: { colorize: boolean; translateTime: string; ignore: string; }; } | undefined' is not assignable to type 'TransportSingleOptions<Record<string, any>> | TransportMultiOptions<Record<string, any>> | TransportPipelineOptions<Record<string, any>>'.
          Type 'undefined' is not assignable to type 'TransportSingleOptions<Record<string, any>> | TransportMultiOptions<Record<string, any>> | TransportPipelineOptions<Record<string, any>>'.

[7m  7[0m export const logger = pino({
[7m   [0m [91m                           ~[0m
[7m  8[0m   level: process.env.LOG_LEVEL || (isDevelopment ? 'debug' : 'info'),
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m 26[0m   },
[7m   [0m [91m~~~~[0m
[7m 27[0m });
[7m   [0m [91m~[0m


[96msrc/index.ts[0m:[93m50[0m:[93m16[0m - [91merror[0m[90m TS7031: [0mBinding element 'path' implicitly has an 'any' type.

[7m50[0m           ? ({ path, error }) => {
[7m  [0m [91m               ~~~~[0m

[96msrc/index.ts[0m:[93m50[0m:[93m22[0m - [91merror[0m[90m TS7031: [0mBinding element 'error' implicitly has an 'any' type.

[7m50[0m           ? ({ path, error }) => {
[7m  [0m [91m                     ~~~~~[0m

[96msrc/middleware/auth.ts[0m:[93m11[0m:[93m14[0m - [91merror[0m[90m TS7022: [0m'verifyAuth' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.

[7m11[0m export const verifyAuth = async (token: string | undefined): Promise<Context['user']> => {
[7m  [0m [91m             ~~~~~~~~~~[0m

[96msrc/middleware/auth.ts[0m:[93m99[0m:[93m7[0m - [91merror[0m[90m TS7053: [0mElement implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ invoice_capturer: number; approver: number; accountant: number; tenant_admin: number; system_admin: number; }'.

[7m99[0m   if (roleLevel[user.role] < roleLevel[requiredRole]) {
[7m  [0m [91m      ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/middleware/auth.ts[0m:[93m99[0m:[93m30[0m - [91merror[0m[90m TS7053: [0mElement implicitly has an 'any' type because expression of type 'UserRole' can't be used to index type '{ invoice_capturer: number; approver: number; accountant: number; tenant_admin: number; system_admin: number; }'.
  Property '[UserRole.SNAPPER]' does not exist on type '{ invoice_capturer: number; approver: number; accountant: number; tenant_admin: number; system_admin: number; }'.

[7m99[0m   if (roleLevel[user.role] < roleLevel[requiredRole]) {
[7m  [0m [91m                             ~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/routes/accounting.ts[0m:[93m54[0m:[93m21[0m - [91merror[0m[90m TS7031: [0mBinding element 'input' implicitly has an 'any' type.

[7m54[0m     .query(async ({ input, ctx }) => {
[7m  [0m [91m                    ~~~~~[0m

[96msrc/routes/accounting.ts[0m:[93m54[0m:[93m28[0m - [91merror[0m[90m TS7031: [0mBinding element 'ctx' implicitly has an 'any' type.

[7m54[0m     .query(async ({ input, ctx }) => {
[7m  [0m [91m                           ~~~[0m

[96msrc/routes/accounting.ts[0m:[93m100[0m:[93m24[0m - [91merror[0m[90m TS7031: [0mBinding element 'input' implicitly has an 'any' type.

[7m100[0m     .mutation(async ({ input, ctx }) => {
[7m   [0m [91m                       ~~~~~[0m

[96msrc/routes/accounting.ts[0m:[93m100[0m:[93m31[0m - [91merror[0m[90m TS7031: [0mBinding element 'ctx' implicitly has an 'any' type.

[7m100[0m     .mutation(async ({ input, ctx }) => {
[7m   [0m [91m                              ~~~[0m

[96msrc/routes/accounting.ts[0m:[93m131[0m:[93m24[0m - [91merror[0m[90m TS7031: [0mBinding element 'input' implicitly has an 'any' type.

[7m131[0m     .mutation(async ({ input, ctx }) => {
[7m   [0m [91m                       ~~~~~[0m

[96msrc/routes/accounting.ts[0m:[93m131[0m:[93m31[0m - [91merror[0m[90m TS7031: [0mBinding element 'ctx' implicitly has an 'any' type.

[7m131[0m     .mutation(async ({ input, ctx }) => {
[7m   [0m [91m                              ~~~[0m

[96msrc/routes/accounting.ts[0m:[93m145[0m:[93m63[0m - [91merror[0m[90m TS2554: [0mExpected 2 arguments, but got 3.

[7m145[0m         await provider.connect(input.code, input.redirectUri, input.realmId);
[7m   [0m [91m                                                              ~~~~~~~~~~~~~[0m

[96msrc/routes/accounting.ts[0m:[93m168[0m:[93m24[0m - [91merror[0m[90m TS7031: [0mBinding element 'input' implicitly has an 'any' type.

[7m168[0m     .mutation(async ({ input, ctx }) => {
[7m   [0m [91m                       ~~~~~[0m

[96msrc/routes/accounting.ts[0m:[93m168[0m:[93m31[0m - [91merror[0m[90m TS7031: [0mBinding element 'ctx' implicitly has an 'any' type.

[7m168[0m     .mutation(async ({ input, ctx }) => {
[7m   [0m [91m                              ~~~[0m

[96msrc/routes/accounting.ts[0m:[93m196[0m:[93m24[0m - [91merror[0m[90m TS7031: [0mBinding element 'input' implicitly has an 'any' type.

[7m196[0m     .mutation(async ({ input, ctx }) => {
[7m   [0m [91m                       ~~~~~[0m

[96msrc/routes/accounting.ts[0m:[93m196[0m:[93m31[0m - [91merror[0m[90m TS7031: [0mBinding element 'ctx' implicitly has an 'any' type.

[7m196[0m     .mutation(async ({ input, ctx }) => {
[7m   [0m [91m                              ~~~[0m

[96msrc/routes/accounting.ts[0m:[93m226[0m:[93m24[0m - [91merror[0m[90m TS7031: [0mBinding element 'input' implicitly has an 'any' type.

[7m226[0m     .mutation(async ({ input, ctx }) => {
[7m   [0m [91m                       ~~~~~[0m

[96msrc/routes/accounting.ts[0m:[93m226[0m:[93m31[0m - [91merror[0m[90m TS7031: [0mBinding element 'ctx' implicitly has an 'any' type.

[7m226[0m     .mutation(async ({ input, ctx }) => {
[7m   [0m [91m                              ~~~[0m

[96msrc/routes/accounting.ts[0m:[93m267[0m:[93m24[0m - [91merror[0m[90m TS7031: [0mBinding element 'input' implicitly has an 'any' type.

[7m267[0m     .mutation(async ({ input, ctx }) => {
[7m   [0m [91m                       ~~~~~[0m

[96msrc/routes/accounting.ts[0m:[93m267[0m:[93m31[0m - [91merror[0m[90m TS7031: [0mBinding element 'ctx' implicitly has an 'any' type.

[7m267[0m     .mutation(async ({ input, ctx }) => {
[7m   [0m [91m                              ~~~[0m

[96msrc/routes/customers.router.ts[0m:[93m97[0m:[93m15[0m - [91merror[0m[90m TS2412: [0mType '{ line1?: string | null | undefined; line2?: string | null | undefined; city?: string | null | undefined; region?: string | null | undefined; postalCode?: string | null | undefined; country?: string | ... 1 more ... | undefined; } | undefined' is not assignable to type '{ line1?: string; line2?: string; city?: string; region?: string; postalCode?: string; country?: string; } | undefined' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.

[7m97[0m               address: customer.address,
[7m  [0m [91m              ~~~~~~~[0m

  [96msrc/services/accounting/types.ts[0m:[93m59[0m:[93m3[0m
    [7m59[0m   address?: {
    [7m  [0m [96m  ~~~~~~~[0m
    The expected type comes from property 'address' which is declared here on type 'CustomerInput'

[96msrc/routes/invoices.router.ts[0m:[93m142[0m:[93m15[0m - [91merror[0m[90m TS7034: [0mVariable 'invoices' implicitly has type 'any[]' in some locations where its type cannot be determined.

[7m142[0m         const invoices = [];
[7m   [0m [91m              ~~~~~~~~[0m

[96msrc/routes/invoices.router.ts[0m:[93m145[0m:[93m26[0m - [91merror[0m[90m TS7006: [0mParameter 'doc' implicitly has an 'any' type.

[7m145[0m         snapshot.forEach(doc => {
[7m   [0m [91m                         ~~~[0m

[96msrc/routes/invoices.router.ts[0m:[93m187[0m:[93m11[0m - [91merror[0m[90m TS7005: [0mVariable 'invoices' implicitly has an 'any[]' type.

[7m187[0m           invoices,
[7m   [0m [91m          ~~~~~~~~[0m

[96msrc/routes/invoices.router.ts[0m:[93m403[0m:[93m54[0m - [91merror[0m[90m TS7006: [0mParameter 'a' implicitly has an 'any' type.

[7m403[0m           attachmentIds: invoiceData.attachments.map(a => a.id),
[7m   [0m [91m                                                     ~[0m

[96msrc/routes/invoices.router.ts[0m:[93m410[0m:[93m18[0m - [91merror[0m[90m TS2551: [0mProperty 'PUBSUB_INVOICE_PROCESS_TOPIC' does not exist on type '{ NODE_ENV: "production" | "development" | "test"; PORT: string; HOST: string; OCR_PROCESSING_MODE: "direct" | "pubsub"; DOCUMENT_AI_LOCATION: string; PUBSUB_INVOICE_PROCESSING_TOPIC: string; ... 12 more ...; XERO_CLIENT_SECRET?: string | undefined; }'. Did you mean 'PUBSUB_INVOICE_PROCESSING_TOPIC'?

[7m410[0m         if (!env.PUBSUB_INVOICE_PROCESS_TOPIC) {
[7m   [0m [91m                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/routes/invoices.router.ts[0m:[93m419[0m:[93m40[0m - [91merror[0m[90m TS2551: [0mProperty 'PUBSUB_INVOICE_PROCESS_TOPIC' does not exist on type '{ NODE_ENV: "production" | "development" | "test"; PORT: string; HOST: string; OCR_PROCESSING_MODE: "direct" | "pubsub"; DOCUMENT_AI_LOCATION: string; PUBSUB_INVOICE_PROCESSING_TOPIC: string; ... 12 more ...; XERO_CLIENT_SECRET?: string | undefined; }'. Did you mean 'PUBSUB_INVOICE_PROCESSING_TOPIC'?

[7m419[0m         const topic = pubsub.topic(env.PUBSUB_INVOICE_PROCESS_TOPIC);
[7m   [0m [91m                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/routes/ocr.router.ts[0m:[93m331[0m:[93m32[0m - [91merror[0m[90m TS2339: [0mProperty 'STORAGE_BUCKET' does not exist on type '{ NODE_ENV: "production" | "development" | "test"; PORT: string; HOST: string; OCR_PROCESSING_MODE: "direct" | "pubsub"; DOCUMENT_AI_LOCATION: string; PUBSUB_INVOICE_PROCESSING_TOPIC: string; ... 12 more ...; XERO_CLIENT_SECRET?: string | undefined; }'.

[7m331[0m         const bucketName = env.STORAGE_BUCKET || 'billsnapp-invoices';
[7m   [0m [91m                               ~~~~~~~~~~~~~~[0m

[96msrc/routes/ocr.router.ts[0m:[93m436[0m:[93m13[0m - [91merror[0m[90m TS2322: [0mType '"FAILED_PRECONDITION"' is not assignable to type '"UNAUTHORIZED" | "PARSE_ERROR" | "BAD_REQUEST" | "INTERNAL_SERVER_ERROR" | "NOT_IMPLEMENTED" | "BAD_GATEWAY" | "SERVICE_UNAVAILABLE" | "GATEWAY_TIMEOUT" | "FORBIDDEN" | "NOT_FOUND" | ... 8 more ... | "CLIENT_CLOSED_REQUEST"'.

[7m436[0m             code: 'FAILED_PRECONDITION',
[7m   [0m [91m            ~~~~[0m

  [96m../../node_modules/.pnpm/@trpc+server@11.0.0_typescript@5.8.3/node_modules/@trpc/server/dist/unstable-core-do-not-import/error/TRPCError.d.ts[0m:[93m9[0m:[93m9[0m
    [7m9[0m         code: TRPC_ERROR_CODE_KEY;
    [7m [0m [96m        ~~~~[0m
    The expected type comes from property 'code' which is declared here on type '{ message?: string; code: "UNAUTHORIZED" | "PARSE_ERROR" | "BAD_REQUEST" | "INTERNAL_SERVER_ERROR" | "NOT_IMPLEMENTED" | "BAD_GATEWAY" | "SERVICE_UNAVAILABLE" | "GATEWAY_TIMEOUT" | ... 10 more ... | "CLIENT_CLOSED_REQUEST"; cause?: unknown; }'

[96msrc/routes/ocr.router.ts[0m:[93m514[0m:[93m13[0m - [91merror[0m[90m TS2322: [0mType '"FAILED_PRECONDITION"' is not assignable to type '"UNAUTHORIZED" | "PARSE_ERROR" | "BAD_REQUEST" | "INTERNAL_SERVER_ERROR" | "NOT_IMPLEMENTED" | "BAD_GATEWAY" | "SERVICE_UNAVAILABLE" | "GATEWAY_TIMEOUT" | "FORBIDDEN" | "NOT_FOUND" | ... 8 more ... | "CLIENT_CLOSED_REQUEST"'.

[7m514[0m             code: 'FAILED_PRECONDITION',
[7m   [0m [91m            ~~~~[0m

  [96m../../node_modules/.pnpm/@trpc+server@11.0.0_typescript@5.8.3/node_modules/@trpc/server/dist/unstable-core-do-not-import/error/TRPCError.d.ts[0m:[93m9[0m:[93m9[0m
    [7m9[0m         code: TRPC_ERROR_CODE_KEY;
    [7m [0m [96m        ~~~~[0m
    The expected type comes from property 'code' which is declared here on type '{ message?: string; code: "UNAUTHORIZED" | "PARSE_ERROR" | "BAD_REQUEST" | "INTERNAL_SERVER_ERROR" | "NOT_IMPLEMENTED" | "BAD_GATEWAY" | "SERVICE_UNAVAILABLE" | "GATEWAY_TIMEOUT" | ... 10 more ... | "CLIENT_CLOSED_REQUEST"; cause?: unknown; }'

[96msrc/routes/tenants.router.ts[0m:[93m104[0m:[93m15[0m - [91merror[0m[90m TS7034: [0mVariable 'tenants' implicitly has type 'any[]' in some locations where its type cannot be determined.

[7m104[0m         const tenants = [];
[7m   [0m [91m              ~~~~~~~[0m

[96msrc/routes/tenants.router.ts[0m:[93m107[0m:[93m26[0m - [91merror[0m[90m TS7006: [0mParameter 'doc' implicitly has an 'any' type.

[7m107[0m         snapshot.forEach(doc => {
[7m   [0m [91m                         ~~~[0m

[96msrc/routes/tenants.router.ts[0m:[93m116[0m:[93m11[0m - [91merror[0m[90m TS7005: [0mVariable 'tenants' implicitly has an 'any[]' type.

[7m116[0m           tenants,
[7m   [0m [91m          ~~~~~~~[0m

[96msrc/routes/vendors.router.ts[0m:[93m64[0m:[93m50[0m - [91merror[0m[90m TS7006: [0mParameter 'doc' implicitly has an 'any' type.

[7m64[0m         const vendors = vendorsSnapshot.docs.map(doc => {
[7m  [0m [91m                                                 ~~~[0m

[96msrc/routes/vendors.router.ts[0m:[93m112[0m:[93m33[0m - [91merror[0m[90m TS2304: [0mCannot find name 'firestore'.

[7m112[0m         const vendorDoc = await firestore.collection('vendors').doc(input.vendorId).get();
[7m   [0m [91m                                ~~~~~~~~~[0m

[96msrc/routes/vendors.router.ts[0m:[93m234[0m:[93m33[0m - [91merror[0m[90m TS2304: [0mCannot find name 'firestore'.

[7m234[0m         const vendorDoc = await firestore.collection('vendors').doc(input.vendorId).get();
[7m   [0m [91m                                ~~~~~~~~~[0m

[96msrc/routes/vendors.router.ts[0m:[93m315[0m:[93m33[0m - [91merror[0m[90m TS2304: [0mCannot find name 'firestore'.

[7m315[0m         const vendorDoc = await firestore.collection('vendors').doc(input.vendorId).get();
[7m   [0m [91m                                ~~~~~~~~~[0m

[96msrc/routes/webhooks.router.ts[0m:[93m12[0m:[93m10[0m - [91merror[0m[90m TS2305: [0mModule '"../trpc"' has no exported member 'createTRPCRouter'.

[7m12[0m import { createTRPCRouter, publicProcedure } from '../trpc';
[7m  [0m [91m         ~~~~~~~~~~~~~~~~[0m

[96msrc/routes/webhooks.router.ts[0m:[93m22[0m:[93m46[0m - [91merror[0m[90m TS7030: [0mNot all code paths return a value.

[7m22[0m webhooksExpressRouter.post('/qbo/:tenantId', async (req, res) => {
[7m  [0m [91m                                             ~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/routes/webhooks.router.ts[0m:[93m62[0m:[93m24[0m - [91merror[0m[90m TS7031: [0mBinding element 'ctx' implicitly has an 'any' type.

[7m62[0m     .mutation(async ({ ctx, input }) => {
[7m  [0m [91m                       ~~~[0m

[96msrc/routes/webhooks.router.ts[0m:[93m62[0m:[93m29[0m - [91merror[0m[90m TS7031: [0mBinding element 'input' implicitly has an 'any' type.

[7m62[0m     .mutation(async ({ ctx, input }) => {
[7m  [0m [91m                            ~~~~~[0m

[96msrc/routes/webhooks.router.ts[0m:[93m67[0m:[93m53[0m - [91merror[0m[90m TS2835: [0mRelative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean '../services/accounting/webhooks/registration.js'?

[7m67[0m         const { registerQboWebhook } = await import('../services/accounting/webhooks/registration');
[7m  [0m [91m                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/routes/webhooks.router.ts[0m:[93m107[0m:[93m24[0m - [91merror[0m[90m TS7031: [0mBinding element 'ctx' implicitly has an 'any' type.

[7m107[0m     .mutation(async ({ ctx, input }) => {
[7m   [0m [91m                       ~~~[0m

[96msrc/routes/webhooks.router.ts[0m:[93m107[0m:[93m29[0m - [91merror[0m[90m TS7031: [0mBinding element 'input' implicitly has an 'any' type.

[7m107[0m     .mutation(async ({ ctx, input }) => {
[7m   [0m [91m                            ~~~~~[0m

[96msrc/routes/webhooks.router.ts[0m:[93m112[0m:[93m60[0m - [91merror[0m[90m TS2835: [0mRelative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean '../services/accounting/webhooks/registration.js'?

[7m112[0m         const { deleteWebhookRegistration } = await import('../services/accounting/webhooks/registration');
[7m   [0m [91m                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/accounting/kmsEncryption.ts[0m:[93m8[0m:[93m44[0m - [91merror[0m[90m TS2307: [0mCannot find module '@google-cloud/kms' or its corresponding type declarations.

[7m8[0m import { KeyManagementServiceClient } from '@google-cloud/kms';
[7m [0m [91m                                           ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/accounting/monitoredProvider.ts[0m:[93m54[0m:[93m49[0m - [91merror[0m[90m TS2683: [0m'this' implicitly has type 'any' because it does not have a type annotation.

[7m54[0m             const result = originalMethod.apply(this, args);
[7m  [0m [91m                                                ~~~~[0m

  [96msrc/services/accounting/monitoredProvider.ts[0m:[93m49[0m:[93m16[0m
    [7m49[0m         return function(...args: any[]) {
    [7m  [0m [96m               ~~~~~~~~[0m
    An outer value of 'this' is shadowed by this container.

[96msrc/services/accounting/monitoredProvider.ts[0m:[93m63[0m:[93m29[0m - [91merror[0m[90m TS2379: [0mArgument of type '{ success: true; entityCount: number | undefined; }' is not assignable to parameter of type '{ success: boolean; errorCode?: string; errorMessage?: string; entityCount?: number; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'entityCount' are incompatible.
    Type 'number | undefined' is not assignable to type 'number'.
      Type 'undefined' is not assignable to type 'number'.

[7m63[0m                   endTiming({
[7m  [0m [91m                            ~[0m
[7m64[0m                     success: true,
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m65[0m                     entityCount
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m66[0m                   });
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/accounting/monitoring.ts[0m:[93m42[0m:[93m14[0m - [91merror[0m[90m TS2503: [0mCannot find namespace 'firebase'.

[7m42[0m   timestamp: firebase.firestore.Timestamp;
[7m  [0m [91m             ~~~~~~~~[0m

[96msrc/services/accounting/monitoring.ts[0m:[93m170[0m:[93m53[0m - [91merror[0m[90m TS7006: [0mParameter 'doc' implicitly has an 'any' type.

[7m170[0m       const failedOperations = snapshot.docs.filter(doc => !doc.data().success).length;
[7m   [0m [91m                                                    ~~~[0m

[96msrc/services/accounting/monitoring.ts[0m:[93m207[0m:[93m50[0m - [91merror[0m[90m TS7006: [0mParameter 'sum' implicitly has an 'any' type.

[7m207[0m       const totalLatency = snapshot.docs.reduce((sum, doc) => sum + doc.data().durationMs, 0);
[7m   [0m [91m                                                 ~~~[0m

[96msrc/services/accounting/monitoring.ts[0m:[93m207[0m:[93m55[0m - [91merror[0m[90m TS7006: [0mParameter 'doc' implicitly has an 'any' type.

[7m207[0m       const totalLatency = snapshot.docs.reduce((sum, doc) => sum + doc.data().durationMs, 0);
[7m   [0m [91m                                                      ~~~[0m

[96msrc/services/accounting/monitoring.ts[0m:[93m242[0m:[93m51[0m - [91merror[0m[90m TS7006: [0mParameter 'sum' implicitly has an 'any' type.

[7m242[0m       const totalEntities = snapshot.docs.reduce((sum, doc) => sum + (doc.data().entityCount || 0), 0);
[7m   [0m [91m                                                  ~~~[0m

[96msrc/services/accounting/monitoring.ts[0m:[93m242[0m:[93m56[0m - [91merror[0m[90m TS7006: [0mParameter 'doc' implicitly has an 'any' type.

[7m242[0m       const totalEntities = snapshot.docs.reduce((sum, doc) => sum + (doc.data().entityCount || 0), 0);
[7m   [0m [91m                                                       ~~~[0m

[96msrc/services/accounting/monitoring.ts[0m:[93m243[0m:[93m53[0m - [91merror[0m[90m TS7006: [0mParameter 'sum' implicitly has an 'any' type.

[7m243[0m       const totalDurationMs = snapshot.docs.reduce((sum, doc) => sum + doc.data().durationMs, 0);
[7m   [0m [91m                                                    ~~~[0m

[96msrc/services/accounting/monitoring.ts[0m:[93m243[0m:[93m58[0m - [91merror[0m[90m TS7006: [0mParameter 'doc' implicitly has an 'any' type.

[7m243[0m       const totalDurationMs = snapshot.docs.reduce((sum, doc) => sum + doc.data().durationMs, 0);
[7m   [0m [91m                                                         ~~~[0m

[96msrc/services/accounting/monitoring.ts[0m:[93m320[0m:[93m23[0m - [91merror[0m[90m TS2379: [0mArgument of type '{ success: true; entityCount: number | undefined; }' is not assignable to parameter of type '{ success: boolean; errorCode?: string; errorMessage?: string; entityCount?: number; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'entityCount' are incompatible.
    Type 'number | undefined' is not assignable to type 'number'.
      Type 'undefined' is not assignable to type 'number'.

[7m320[0m       await endTiming({
[7m   [0m [91m                      ~[0m
[7m321[0m         success: true,
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~[0m
[7m322[0m         entityCount
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~[0m
[7m323[0m       });
[7m   [0m [91m~~~~~~~[0m

[96msrc/services/accounting/push/pushWorker.ts[0m:[93m255[0m:[93m11[0m - [91merror[0m[90m TS2741: [0mProperty 'transactionType' is missing in type '{ tenantId: string; invoiceId: string; providerType: string; idempotencyKey: string; }' but required in type 'PushTaskPayload'.

[7m255[0m     const payload: PushTaskPayload = {
[7m   [0m [91m          ~~~~~~~[0m

  [96msrc/services/accounting/push/pushWorker.ts[0m:[93m26[0m:[93m3[0m
    [7m26[0m   transactionType: TransactionType;
    [7m  [0m [96m  ~~~~~~~~~~~~~~~[0m
    'transactionType' is declared here.

[96msrc/services/accounting/push/pushWorker.ts[0m:[93m278[0m:[93m30[0m - [91merror[0m[90m TS2339: [0mProperty 'run' does not exist on type 'HttpsFunction'.

[7m278[0m     await pushWorkerFunction.run(mockReq, mockRes);
[7m   [0m [91m                             ~~~[0m

[96msrc/services/accounting/push/pushWorker.ts[0m:[93m296[0m:[93m11[0m - [91merror[0m[90m TS2741: [0mProperty 'transactionType' is missing in type '{ tenantId: string; invoiceId: string; providerType: string; idempotencyKey: string; }' but required in type 'PushTaskPayload'.

[7m296[0m     const payload: PushTaskPayload = {
[7m   [0m [91m          ~~~~~~~[0m

  [96msrc/services/accounting/push/pushWorker.ts[0m:[93m26[0m:[93m3[0m
    [7m26[0m   transactionType: TransactionType;
    [7m  [0m [96m  ~~~~~~~~~~~~~~~[0m
    'transactionType' is declared here.

[96msrc/services/accounting/push/pushWorker.ts[0m:[93m319[0m:[93m30[0m - [91merror[0m[90m TS2339: [0mProperty 'run' does not exist on type 'HttpsFunction'.

[7m319[0m     await pushWorkerFunction.run(mockReq, mockRes);
[7m   [0m [91m                             ~~~[0m

[96msrc/services/accounting/push/taskClient.ts[0m:[93m7[0m:[93m34[0m - [91merror[0m[90m TS2307: [0mCannot find module '@google-cloud/tasks' or its corresponding type declarations.

[7m7[0m import { CloudTasksClient } from '@google-cloud/tasks';
[7m [0m [91m                                 ~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/accounting/qbo/index.ts[0m:[93m168[0m:[93m9[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'tokenExpiry' does not exist in type 'Partial<ProviderTokens>'.

[7m168[0m         tokenExpiry: new Date(Date.now() + tokens.expires_in * 1000).toISOString(),
[7m   [0m [91m        ~~~~~~~~~~~[0m

[96msrc/services/accounting/qbo/index.ts[0m:[93m207[0m:[93m9[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'tokenExpiry' does not exist in type 'Partial<ProviderTokens>'.

[7m207[0m         tokenExpiry: new Date(Date.now() + tokens.expires_in * 1000).toISOString(),

[7m   [0m [91m        ~~~~~~~~~~~[0m

[96msrc/services/accounting/qbo/index.ts[0m:[93m774[0m:[93m9[0m - [91merror[0m[90m TS2322: [0mType '"DISCONNECTED"' is not assignable to type '"OK" | "ERROR"'.

[7m774[0m         status: 'DISCONNECTED',
[7m   [0m [91m        ~~~~~~[0m

  [96msrc/services/accounting/tokenStore.ts[0m:[93m55[0m:[93m3[0m
    [7m55[0m   status: 'OK' | 'ERROR';
    [7m  [0m [96m  ~~~~~~[0m
    The expected type comes from property 'status' which is declared here on type 'Partial<ProviderTokens>'

[96msrc/services/accounting/qbo/mapper.ts[0m:[93m70[0m:[93m3[0m - [91merror[0m[90m TS2375: [0mType '{ id: any; name: any; email: any; phone: any; taxId: any; address: { line1: any; line2: any; city: any; region: any; postalCode: any; country: any; } | undefined; active: any; providerId: any; }' is not assignable to type 'VendorDTO' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'address' are incompatible.
    Type '{ line1: any; line2: any; city: any; region: any; postalCode: any; country: any; } | undefined' is not assignable to type '{ line1?: string; line2?: string; city?: string; region?: string; postalCode?: string; country?: string; }'.
      Type 'undefined' is not assignable to type '{ line1?: string; line2?: string; city?: string; region?: string; postalCode?: string; country?: string; }'.

[7m70[0m   return {
[7m  [0m [91m  ~~~~~~[0m

[96msrc/services/accounting/qbo/mapper.ts[0m:[93m129[0m:[93m3[0m - [91merror[0m[90m TS2375: [0mType '{ id: any; name: any; email: any; phone: any; taxId: any; address: { line1: any; line2: any; city: any; region: any; postalCode: any; country: any; } | undefined; active: any; providerId: any; }' is not assignable to type 'CustomerDTO' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'address' are incompatible.
    Type '{ line1: any; line2: any; city: any; region: any; postalCode: any; country: any; } | undefined' is not assignable to type '{ line1?: string; line2?: string; city?: string; region?: string; postalCode?: string; country?: string; }'.
      Type 'undefined' is not assignable to type '{ line1?: string; line2?: string; city?: string; region?: string; postalCode?: string; country?: string; }'.

[7m129[0m   return {
[7m   [0m [91m  ~~~~~~[0m

[96msrc/services/accounting/qbo/sdk.ts[0m:[93m6[0m:[93m25[0m - [91merror[0m[90m TS2307: [0mCannot find module 'intuit-oauth' or its corresponding type declarations.

[7m6[0m import IntuitOAuth from 'intuit-oauth';
[7m [0m [91m                        ~~~~~~~~~~~~~~[0m

[96msrc/services/accounting/status/transactionStatus.ts[0m:[93m176[0m:[93m40[0m - [91merror[0m[90m TS2339: [0mProperty 'getBill' does not exist on type 'AccountingProvider'.

[7m176[0m       transactionData = await provider.getBill(providerId);
[7m   [0m [91m                                       ~~~~~~~[0m

[96msrc/services/accounting/status/transactionStatus.ts[0m:[93m178[0m:[93m40[0m - [91merror[0m[90m TS2339: [0mProperty 'getInvoice' does not exist on type 'AccountingProvider'.

[7m178[0m       transactionData = await provider.getInvoice(providerId);
[7m   [0m [91m                                       ~~~~~~~~~~[0m

[96msrc/services/accounting/status/transactionStatus.ts[0m:[93m214[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m214[0m       operation: OperationType.TRANSACTION_STATUS,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/status/transactionStatus.ts[0m:[93m232[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m232[0m       operation: OperationType.TRANSACTION_STATUS,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/status/transactionStatus.ts[0m:[93m335[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m335[0m       operation: OperationType.TRANSACTION_BATCH,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/status/transactionStatus.ts[0m:[93m355[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m355[0m       operation: OperationType.TRANSACTION_BATCH,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/status/transactionStatus.ts[0m:[93m401[0m:[93m54[0m - [91merror[0m[90m TS7006: [0mParameter 'doc' implicitly has an 'any' type.

[7m401[0m   const recentPushed = recentPushedSnapshot.docs.map(doc => ({
[7m   [0m [91m                                                     ~~~[0m

[96msrc/services/accounting/status/transactionStatus.ts[0m:[93m422[0m:[93m50[0m - [91merror[0m[90m TS7006: [0mParameter 'doc' implicitly has an 'any' type.

[7m422[0m   const unchecked = lastCheckedSnapshot.docs.map(doc => ({
[7m   [0m [91m                                                 ~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m191[0m:[93m30[0m - [91merror[0m[90m TS7006: [0mParameter 'doc' implicitly has an 'any' type.

[7m191[0m     return snapshot.docs.map(doc => doc.data() as SyncChange);
[7m   [0m [91m                             ~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m271[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'upsertAccount' does not exist on type 'AccountingProvider'.

[7m271[0m             await provider.upsertAccount(change.data);
[7m   [0m [91m                           ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m277[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'deleteAccount' does not exist on type 'AccountingProvider'.

[7m277[0m             await provider.deleteAccount(change.providerId);
[7m   [0m [91m                           ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m288[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'upsertVendor' does not exist on type 'AccountingProvider'.

[7m288[0m             await provider.upsertVendor(change.data);
[7m   [0m [91m                           ~~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m294[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'deleteVendor' does not exist on type 'AccountingProvider'.

[7m294[0m             await provider.deleteVendor(change.providerId);
[7m   [0m [91m                           ~~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m305[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'upsertCustomer' does not exist on type 'AccountingProvider'.

[7m305[0m             await provider.upsertCustomer(change.data);
[7m   [0m [91m                           ~~~~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m311[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'deleteCustomer' does not exist on type 'AccountingProvider'.

[7m311[0m             await provider.deleteCustomer(change.providerId);
[7m   [0m [91m                           ~~~~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m333[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'pushInvoice' does not exist on type 'AccountingProvider'.

[7m333[0m             await provider.pushInvoice(change.data);
[7m   [0m [91m                           ~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m404[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m404[0m       operation: OperationType.SYNC_DELTA,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m424[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m424[0m       operation: OperationType.SYNC_DELTA,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m487[0m:[93m36[0m - [91merror[0m[90m TS2339: [0mProperty 'accountType' does not exist on type 'AccountDTO'.

[7m487[0m               accountType: account.accountType,
[7m   [0m [91m                                   ~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m488[0m:[93m31[0m - [91merror[0m[90m TS2339: [0mProperty 'number' does not exist on type 'AccountDTO'.

[7m488[0m               number: account.number,
[7m   [0m [91m                              ~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m490[0m:[93m33[0m - [91merror[0m[90m TS2551: [0mProperty 'isActive' does not exist on type 'AccountDTO'. Did you mean 'active'?

[7m490[0m               isActive: account.isActive,
[7m   [0m [91m                                ~~~~~~~~[0m

  [96msrc/services/accounting/types.ts[0m:[93m29[0m:[93m3[0m
    [7m29[0m   active: boolean;
    [7m  [0m [96m  ~~~~~~[0m
    'active' is declared here.

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m502[0m:[93m36[0m - [91merror[0m[90m TS2339: [0mProperty 'accountType' does not exist on type 'AccountDTO'.

[7m502[0m               accountType: account.accountType,
[7m   [0m [91m                                   ~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m503[0m:[93m31[0m - [91merror[0m[90m TS2339: [0mProperty 'number' does not exist on type 'AccountDTO'.

[7m503[0m               number: account.number,
[7m   [0m [91m                              ~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m505[0m:[93m33[0m - [91merror[0m[90m TS2551: [0mProperty 'isActive' does not exist on type 'AccountDTO'. Did you mean 'active'?

[7m505[0m               isActive: account.isActive,
[7m   [0m [91m                                ~~~~~~~~[0m

  [96msrc/services/accounting/types.ts[0m:[93m29[0m:[93m3[0m
    [7m29[0m   active: boolean;
    [7m  [0m [96m  ~~~~~~[0m
    'active' is declared here.

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m631[0m:[93m7[0m - [91merror[0m[90m TS2412: [0mType 'undefined' is not assignable to type 'string' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.

[7m631[0m       meta.lastError = undefined;
[7m   [0m [91m      ~~~~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m641[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m641[0m       operation: OperationType.SYNC_FULL,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/deltaSync.ts[0m:[93m662[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m662[0m       operation: OperationType.SYNC_FULL,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/sync/nightlySync.ts[0m:[93m8[0m:[93m10[0m - [91merror[0m[90m TS2305: [0mModule '"../../../config/firebase"' has no exported member 'db'.

[7m8[0m import { db } from '../../../config/firebase';
[7m [0m [91m         ~~[0m

[96msrc/services/accounting/sync/nightlySync.ts[0m:[93m113[0m:[93m20[0m - [91merror[0m[90m TS7006: [0mParameter 'doc' implicitly has an 'any' type.

[7m113[0m   snapshot.forEach(doc => {
[7m   [0m [91m                   ~~~[0m

[96msrc/services/accounting/sync/webhookListener.ts[0m:[93m9[0m:[93m10[0m - [91merror[0m[90m TS2305: [0mModule '"../../../config/firebase"' has no exported member 'db'.

[7m9[0m import { db } from '../../../config/firebase';
[7m [0m [91m         ~~[0m

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m151[0m:[93m41[0m - [91merror[0m[90m TS2339: [0mProperty 'accountType' does not exist on type 'AccountDTO'.

[7m151[0m             accountType: updatedAccount.accountType,
[7m   [0m [91m                                        ~~~~~~~~~~~[0m

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m152[0m:[93m36[0m - [91merror[0m[90m TS2339: [0mProperty 'number' does not exist on type 'AccountDTO'.

[7m152[0m             number: updatedAccount.number,
[7m   [0m [91m                                   ~~~~~~[0m

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m154[0m:[93m38[0m - [91merror[0m[90m TS2551: [0mProperty 'isActive' does not exist on type 'AccountDTO'. Did you mean 'active'?

[7m154[0m             isActive: updatedAccount.isActive,
[7m   [0m [91m                                     ~~~~~~~~[0m

  [96msrc/services/accounting/types.ts[0m:[93m29[0m:[93m3[0m
    [7m29[0m   active: boolean;
    [7m  [0m [96m  ~~~~~~[0m
    'active' is declared here.

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m171[0m:[93m41[0m - [91merror[0m[90m TS2339: [0mProperty 'accountType' does not exist on type 'AccountDTO'.

[7m171[0m             accountType: updatedAccount.accountType,
[7m   [0m [91m                                        ~~~~~~~~~~~[0m

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m172[0m:[93m36[0m - [91merror[0m[90m TS2339: [0mProperty 'number' does not exist on type 'AccountDTO'.

[7m172[0m             number: updatedAccount.number,
[7m   [0m [91m                                   ~~~~~~[0m

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m174[0m:[93m38[0m - [91merror[0m[90m TS2551: [0mProperty 'isActive' does not exist on type 'AccountDTO'. Did you mean 'active'?

[7m174[0m             isActive: updatedAccount.isActive,
[7m   [0m [91m                                     ~~~~~~~~[0m

  [96msrc/services/accounting/types.ts[0m:[93m29[0m:[93m3[0m
    [7m29[0m   active: boolean;
    [7m  [0m [96m  ~~~~~~[0m
    'active' is declared here.

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m193[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m193[0m       operation: OperationType.SYNC_DELTA,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m204[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m204[0m       operation: OperationType.SYNC_DELTA,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m313[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m313[0m       operation: OperationType.SYNC_DELTA,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m324[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m324[0m       operation: OperationType.SYNC_DELTA,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m433[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m433[0m       operation: OperationType.SYNC_DELTA,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m444[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m444[0m       operation: OperationType.SYNC_DELTA,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m518[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m518[0m       operation: OperationType.SYNC_DELTA,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/webhooks/handlers.ts[0m:[93m529[0m:[93m18[0m - [91merror[0m[90m TS2693: [0m'OperationType' only refers to a type, but is being used as a value here.

[7m529[0m       operation: OperationType.SYNC_DELTA,
[7m   [0m [91m                 ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/webhooks/registration.ts[0m:[93m42[0m:[93m45[0m - [91merror[0m[90m TS2551: [0mProperty 'updateWebhook' does not exist on type 'AccountingProvider'. Did you mean 'parseWebhook'?

[7m42[0m       const updatedWebhook = await provider.updateWebhook({
[7m  [0m [91m                                            ~~~~~~~~~~~~~[0m

  [96msrc/services/accounting/provider.ts[0m:[93m143[0m:[93m12[0m
    [7m143[0m   abstract parseWebhook(payload: unknown): WebhookEvent[];
    [7m   [0m [96m           ~~~~~~~~~~~~[0m
    'parseWebhook' is declared here.

[96msrc/services/accounting/webhooks/registration.ts[0m:[93m79[0m:[93m41[0m - [91merror[0m[90m TS2339: [0mProperty 'createWebhook' does not exist on type 'AccountingProvider'.

[7m79[0m       const newWebhook = await provider.createWebhook({
[7m  [0m [91m                                        ~~~~~~~~~~~~~[0m

[96msrc/services/accounting/webhooks/registration.ts[0m:[93m183[0m:[93m35[0m - [91merror[0m[90m TS2339: [0mProperty 'deleteWebhook' does not exist on type 'AccountingProvider'.

[7m183[0m     const result = await provider.deleteWebhook(existingWebhook.webhookId);
[7m   [0m [91m                                  ~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/integration.test.ts[0m:[93m112[0m:[93m21[0m - [91merror[0m[90m TS2339: [0mProperty 'processingTimeMs' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m112[0m       expect(result.processingTimeMs).toBeGreaterThan(0);
[7m   [0m [91m                    ~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/integration.test.ts[0m:[93m154[0m:[93m21[0m - [91merror[0m[90m TS2339: [0mProperty 'allTierResults' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m154[0m       expect(result.allTierResults).toBeDefined();
[7m   [0m [91m                    ~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/integration.test.ts[0m:[93m155[0m:[93m21[0m - [91merror[0m[90m TS2339: [0mProperty 'allTierResults' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m155[0m       expect(result.allTierResults).toHaveLength(3); // All three tiers attempted
[7m   [0m [91m                    ~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/integration.test.ts[0m:[93m223[0m:[93m21[0m - [91merror[0m[90m TS2339: [0mProperty 'processingTimeMs' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m223[0m       expect(result.processingTimeMs).toBeLessThan(totalTime);
[7m   [0m [91m                    ~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m40[0m:[93m48[0m - [91merror[0m[90m TS2694: [0mNamespace 'global.jest' has no exported member 'MockedClass'.

[7m40[0m   const MockedTier1 = Tier1OcrProvider as jest.MockedClass<typeof Tier1OcrProvider>;
[7m  [0m [91m                                               ~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m41[0m:[93m48[0m - [91merror[0m[90m TS2694: [0mNamespace 'global.jest' has no exported member 'MockedClass'.

[7m41[0m   const MockedTier2 = Tier2OcrProvider as jest.MockedClass<typeof Tier2OcrProvider>;
[7m  [0m [91m                                               ~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m42[0m:[93m48[0m - [91merror[0m[90m TS2694: [0mNamespace 'global.jest' has no exported member 'MockedClass'.

[7m42[0m   const MockedTier3 = Tier3OcrProvider as jest.MockedClass<typeof Tier3OcrProvider>;
[7m  [0m [91m                                               ~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m44[0m:[93m50[0m - [91merror[0m[90m TS2724: [0m'global.jest' has no exported member named 'Mocked'. Did you mean 'Mock'?

[7m44[0m     tier1: MockedTier1.mock.instances[0] as jest.Mocked<Tier1OcrProvider>,
[7m  [0m [91m                                                 ~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m45[0m:[93m50[0m - [91merror[0m[90m TS2724: [0m'global.jest' has no exported member named 'Mocked'. Did you mean 'Mock'?

[7m45[0m     tier2: MockedTier2.mock.instances[0] as jest.Mocked<Tier2OcrProvider>,
[7m  [0m [91m                                                 ~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m46[0m:[93m50[0m - [91merror[0m[90m TS2724: [0m'global.jest' has no exported member named 'Mocked'. Did you mean 'Mock'?

[7m46[0m     tier3: MockedTier3.mock.instances[0] as jest.Mocked<Tier3OcrProvider>,
[7m  [0m [91m                                                 ~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m75[0m:[93m43[0m - [91merror[0m[90m TS2345: [0mArgument of type 'undefined' is not assignable to parameter of type 'never'.

[7m75[0m     mockFirestoreUpdate.mockResolvedValue(undefined);
[7m  [0m [91m                                          ~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m76[0m:[93m41[0m - [91merror[0m[90m TS2345: [0mArgument of type 'undefined' is not assignable to parameter of type 'never'.

[7m76[0m     mockPubSubPublish.mockResolvedValue(undefined);
[7m  [0m [91m                                        ~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m92[0m:[93m76[0m - [91merror[0m[90m TS2345: [0mArgument of type 'QualityCheckResult' is not assignable to parameter of type 'never'.

[7m92[0m     (orchestrator as any).checkResultQuality = jest.fn().mockResolvedValue({ passed: true, score: 0.95 } as QualityCheckResult);
[7m  [0m [91m                                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m126[0m:[93m30[0m - [91merror[0m[90m TS2345: [0mArgument of type 'QualityCheckResult' is not assignable to parameter of type 'never'.

[7m126[0m       .mockResolvedValueOnce({ passed: false, score: 0.5, shouldTryNextTier: true } as QualityCheckResult) // Tier 1 fails quality
[7m   [0m [91m                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m127[0m:[93m30[0m - [91merror[0m[90m TS2345: [0mArgument of type 'QualityCheckResult' is not assignable to parameter of type 'never'.

[7m127[0m       .mockResolvedValueOnce({ passed: true, score: 0.9 } as QualityCheckResult); // Tier 2 passes quality
[7m   [0m [91m                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m155[0m:[93m76[0m - [91merror[0m[90m TS2345: [0mArgument of type 'QualityCheckResult' is not assignable to parameter of type 'never'.

[7m155[0m     (orchestrator as any).checkResultQuality = jest.fn().mockResolvedValue({ passed: false, score: 0.1, shouldTryNextTier: true } as QualityCheckResult);
[7m   [0m [91m                                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m175[0m:[93m76[0m - [91merror[0m[90m TS2345: [0mArgument of type 'QualityCheckResult' is not assignable to parameter of type 'never'.

[7m175[0m     (orchestrator as any).checkResultQuality = jest.fn().mockResolvedValue({ passed: true, score: 0.9 } as QualityCheckResult);
[7m   [0m [91m                                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/orchestrator.test.ts[0m:[93m195[0m:[93m76[0m - [91merror[0m[90m TS2345: [0mArgument of type 'QualityCheckResult' is not assignable to parameter of type 'never'.

[7m195[0m     (orchestrator as any).checkResultQuality = jest.fn().mockResolvedValue({ passed: true, score: 0.95 } as QualityCheckResult);
[7m   [0m [91m                                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/simple-integration.test.ts[0m:[93m118[0m:[93m18[0m - [91merror[0m[90m TS2339: [0mProperty 'allTierResults' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m118[0m       if (result.allTierResults) {
[7m   [0m [91m                 ~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/simple-integration.test.ts[0m:[93m119[0m:[93m37[0m - [91merror[0m[90m TS2339: [0mProperty 'allTierResults' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m119[0m         expect(Array.isArray(result.allTierResults)).toBe(true);
[7m   [0m [91m                                    ~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/simple-integration.test.ts[0m:[93m168[0m:[93m16[0m - [91merror[0m[90m TS18048: [0m'result.error' is possibly 'undefined'.

[7m168[0m         expect(result.error.length).toBeGreaterThan(0);
[7m   [0m [91m               ~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier1-provider.test.ts[0m:[93m10[0m:[93m30[0m - [91merror[0m[90m TS2724: [0m'global.jest' has no exported member named 'Mocked'. Did you mean 'Mock'?

[7m10[0m   let mockVisionClient: jest.Mocked<vision.ImageAnnotatorClient>;
[7m  [0m [91m                             ~~~~~~[0m

[96msrc/services/ocr/__tests__/tier1-provider.test.ts[0m:[93m27[0m:[93m26[0m - [91merror[0m[90m TS2724: [0m'global.jest' has no exported member named 'Mocked'. Did you mean 'Mock'?

[7m27[0m     } as unknown as jest.Mocked<vision.ImageAnnotatorClient>;
[7m  [0m [91m                         ~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m33[0m:[93m6[0m - [91merror[0m[90m TS2352: [0mConversion of type 'typeof VertexAI' to type 'Mock<any>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'typeof VertexAI' is missing the following properties from type 'Mock<any>': mockResolvedValue, mockResolvedValueOnce, mockImplementation, mockImplementationOnce

[7m33[0m     (VertexAI as jest.Mock).mockImplementation(() => mockVertexAI);
[7m  [0m [91m     ~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m79[0m:[93m61[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ response: { candidates: { content: { parts: { text: string; }[]; }; }[]; }; }' is not assignable to parameter of type 'never'.

[7m79[0m     const mockGenerateContent = jest.fn().mockResolvedValue(mockGeminiResponse);
[7m  [0m [91m                                                            ~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m84[0m:[93m33[0m - [91merror[0m[90m TS2352: [0mConversion of type 'typeof VertexAI' to type 'Mock<any>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'typeof VertexAI' is missing the following properties from type 'Mock<any>': mockResolvedValue, mockResolvedValueOnce, mockImplementation, mockImplementationOnce

[7m84[0m     const mockVertexInstance = (VertexAI as jest.Mock).mock.results[0].value;
[7m  [0m [91m                                ~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m84[0m:[93m56[0m - [91merror[0m[90m TS2339: [0mProperty 'mock' does not exist on type 'Mock<any>'.

[7m84[0m     const mockVertexInstance = (VertexAI as jest.Mock).mock.results[0].value;
[7m  [0m [91m                                                       ~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m92[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'tier' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m92[0m     expect(result.tier).toBe(OcrTier.TIER_2);
[7m  [0m [91m                  ~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m93[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'invoiceData' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m93[0m     expect(result.invoiceData).toBeDefined();
[7m  [0m [91m                  ~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m94[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'invoiceData' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m94[0m     expect(result.invoiceData?.vendorName).toBe('ABC Company');
[7m  [0m [91m                  ~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m95[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'invoiceData' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m95[0m     expect(result.invoiceData?.invoiceNumber).toBe('INV-456');
[7m  [0m [91m                  ~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m96[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'processingTimeMs' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m96[0m     expect(result.processingTimeMs).toBeGreaterThan(0);
[7m  [0m [91m                  ~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m114[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'tier' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m114[0m     expect(result.tier).toBe(OcrTier.TIER_2);
[7m   [0m [91m                  ~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m116[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'processingTimeMs' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m116[0m     expect(result.processingTimeMs).toBeGreaterThan(0);
[7m   [0m [91m                  ~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m127[0m:[93m7[0m - [91merror[0m[90m TS2345: [0mArgument of type 'Error' is not assignable to parameter of type 'never'.

[7m127[0m       new Error('Model inference error')
[7m   [0m [91m      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m134[0m:[93m33[0m - [91merror[0m[90m TS2352: [0mConversion of type 'typeof VertexAI' to type 'Mock<any>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'typeof VertexAI' is missing the following properties from type 'Mock<any>': mockResolvedValue, mockResolvedValueOnce, mockImplementation, mockImplementationOnce

[7m134[0m     const mockVertexInstance = (VertexAI as jest.Mock).mock.results[0].value;
[7m   [0m [91m                                ~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m134[0m:[93m56[0m - [91merror[0m[90m TS2339: [0mProperty 'mock' does not exist on type 'Mock<any>'.

[7m134[0m     const mockVertexInstance = (VertexAI as jest.Mock).mock.results[0].value;
[7m   [0m [91m                                                       ~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m142[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'tier' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m142[0m     expect(result.tier).toBe(OcrTier.TIER_2);
[7m   [0m [91m                  ~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m144[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'processingTimeMs' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m144[0m     expect(result.processingTimeMs).toBeGreaterThan(0);
[7m   [0m [91m                  ~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m171[0m:[93m61[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ response: { candidates: { content: { parts: { text: string; }[]; }; }[]; }; }' is not assignable to parameter of type 'never'.

[7m171[0m     const mockGenerateContent = jest.fn().mockResolvedValue(mockGeminiResponse);
[7m   [0m [91m                                                            ~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m176[0m:[93m33[0m - [91merror[0m[90m TS2352: [0mConversion of type 'typeof VertexAI' to type 'Mock<any>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'typeof VertexAI' is missing the following properties from type 'Mock<any>': mockResolvedValue, mockResolvedValueOnce, mockImplementation, mockImplementationOnce

[7m176[0m     const mockVertexInstance = (VertexAI as jest.Mock).mock.results[0].value;
[7m   [0m [91m                                ~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m176[0m:[93m56[0m - [91merror[0m[90m TS2339: [0mProperty 'mock' does not exist on type 'Mock<any>'.

[7m176[0m     const mockVertexInstance = (VertexAI as jest.Mock).mock.results[0].value;
[7m   [0m [91m                                                       ~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m184[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'tier' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m184[0m     expect(result.tier).toBe(OcrTier.TIER_2);
[7m   [0m [91m                  ~~~~[0m

[96msrc/services/ocr/__tests__/tier2-provider.test.ts[0m:[93m186[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'processingTimeMs' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m186[0m     expect(result.processingTimeMs).toBeGreaterThan(0);
[7m   [0m [91m                  ~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m10[0m:[93m34[0m - [91merror[0m[90m TS2724: [0m'global.jest' has no exported member named 'Mocked'. Did you mean 'Mock'?

[7m10[0m   let mockDocumentAIClient: jest.Mocked<documentai.DocumentProcessorServiceClient>;
[7m  [0m [91m                                 ~~~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m26[0m:[93m26[0m - [91merror[0m[90m TS2724: [0m'global.jest' has no exported member named 'Mocked'. Did you mean 'Mock'?

[7m26[0m     } as unknown as jest.Mocked<documentai.DocumentProcessorServiceClient>;
[7m  [0m [91m                         ~~~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m28[0m:[93m6[0m - [91merror[0m[90m TS2352: [0mConversion of type 'typeof DocumentProcessorServiceClient' to type 'Mock<any>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'typeof DocumentProcessorServiceClient' is missing the following properties from type 'Mock<any>': mockResolvedValue, mockResolvedValueOnce, mockImplementation, mockImplementationOnce

[7m28[0m     (documentai.DocumentProcessorServiceClient as jest.Mock) = jest.fn(() => mockDocumentAIClient);
[7m  [0m [91m     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m87[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'tier' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m87[0m     expect(result.tier).toBe(OcrTier.TIER_3);
[7m  [0m [91m                  ~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m88[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'invoiceData' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m88[0m     expect(result.invoiceData).toBeDefined();
[7m  [0m [91m                  ~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m89[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'invoiceData' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m89[0m     expect(result.invoiceData?.vendorName).toBe('XYZ Corporation');
[7m  [0m [91m                  ~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m90[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'invoiceData' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m90[0m     expect(result.invoiceData?.invoiceNumber).toBe('INV-789');
[7m  [0m [91m                  ~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m91[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'processingTimeMs' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m91[0m     expect(result.processingTimeMs).toBeGreaterThan(0);
[7m  [0m [91m                  ~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m108[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'tier' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m108[0m     expect(result.tier).toBe(OcrTier.TIER_3);
[7m   [0m [91m                  ~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m110[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'processingTimeMs' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m110[0m     expect(result.processingTimeMs).toBeGreaterThan(0);
[7m   [0m [91m                  ~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m129[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'tier' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m129[0m     expect(result.tier).toBe(OcrTier.TIER_3);
[7m   [0m [91m                  ~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m131[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'processingTimeMs' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m131[0m     expect(result.processingTimeMs).toBeGreaterThan(0);
[7m   [0m [91m                  ~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m148[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'tier' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m148[0m     expect(result.tier).toBe(OcrTier.TIER_3);
[7m   [0m [91m                  ~~~~[0m

[96msrc/services/ocr/__tests__/tier3-provider.test.ts[0m:[93m150[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'processingTimeMs' does not exist on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'.

[7m150[0m     expect(result.processingTimeMs).toBeGreaterThan(0);
[7m   [0m [91m                  ~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/quality-check.ts[0m:[93m364[0m:[93m28[0m - [91merror[0m[90m TS2533: [0mObject is possibly 'null' or 'undefined'.

[7m364[0m           sumConfidence += detections[i].confidence;
[7m   [0m [91m                           ~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/quality-check.ts[0m:[93m448[0m:[93m28[0m - [91merror[0m[90m TS2533: [0mObject is possibly 'null' or 'undefined'.

[7m448[0m                 const dx = vertices[3].x - vertices[2].x;
[7m   [0m [91m                           ~~~~~~~~~~~~~[0m

[96msrc/services/ocr/quality-check.ts[0m:[93m448[0m:[93m44[0m - [91merror[0m[90m TS2533: [0mObject is possibly 'null' or 'undefined'.

[7m448[0m                 const dx = vertices[3].x - vertices[2].x;
[7m   [0m [91m                                           ~~~~~~~~~~~~~[0m

[96msrc/services/ocr/quality-check.ts[0m:[93m449[0m:[93m28[0m - [91merror[0m[90m TS2533: [0mObject is possibly 'null' or 'undefined'.

[7m449[0m                 const dy = vertices[3].y - vertices[2].y;
[7m   [0m [91m                           ~~~~~~~~~~~~~[0m

[96msrc/services/ocr/quality-check.ts[0m:[93m449[0m:[93m44[0m - [91merror[0m[90m TS2533: [0mObject is possibly 'null' or 'undefined'.

[7m449[0m                 const dy = vertices[3].y - vertices[2].y;
[7m   [0m [91m                                           ~~~~~~~~~~~~~[0m

[96msrc/services/ocr/tier1-provider.ts[0m:[93m25[0m:[93m32[0m - [91merror[0m[90m TS2551: [0mProperty 'GEMMA_OCR_SERVICE_ENDPOINT' does not exist on type '{ NODE_ENV: "production" | "development" | "test"; PORT: string; HOST: string; OCR_PROCESSING_MODE: "direct" | "pubsub"; DOCUMENT_AI_LOCATION: string; PUBSUB_INVOICE_PROCESSING_TOPIC: string; ... 12 more ...; XERO_CLIENT_SECRET?: string | undefined; }'. Did you mean 'GEMMA_OCR_ENDPOINT'?

[7m25[0m     this.serviceEndpoint = env.GEMMA_OCR_SERVICE_ENDPOINT || 'http://gemma-ocr-service.internal';
[7m  [0m [91m                               ~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/tier1-provider.ts[0m:[93m61[0m:[93m11[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'textExtractionConfidence' does not exist in type '{ processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; }'.

[7m61[0m           textExtractionConfidence: textExtractionResult.confidence
[7m  [0m [91m          ~~~~~~~~~~~~~~~~~~~~~~~~[0m

  [96m../../packages/shared-types/dist/index.d.ts[0m:[93m1122[0m:[93m5[0m
    [7m1122[0m     metadata?: {
    [7m    [0m [96m    ~~~~~~~~[0m
    The expected type comes from property 'metadata' which is declared here on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'

[96msrc/services/ocr/tier2-provider.ts[0m:[93m30[0m:[93m26[0m - [91merror[0m[90m TS2339: [0mProperty 'VERTEX_AI_LOCATION' does not exist on type '{ NODE_ENV: "production" | "development" | "test"; PORT: string; HOST: string; OCR_PROCESSING_MODE: "direct" | "pubsub"; DOCUMENT_AI_LOCATION: string; PUBSUB_INVOICE_PROCESSING_TOPIC: string; ... 12 more ...; XERO_CLIENT_SECRET?: string | undefined; }'.

[7m30[0m     const location = env.VERTEX_AI_LOCATION || 'us-central1';
[7m  [0m [91m                         ~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/tier2-provider.ts[0m:[93m72[0m:[93m11[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'textExtractionConfidence' does not exist in type '{ processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; }'.

[7m72[0m           textExtractionConfidence: textExtractionResult.confidence
[7m  [0m [91m          ~~~~~~~~~~~~~~~~~~~~~~~~[0m

  [96m../../packages/shared-types/dist/index.d.ts[0m:[93m1122[0m:[93m5[0m
    [7m1122[0m     metadata?: {
    [7m    [0m [96m    ~~~~~~~~[0m
    The expected type comes from property 'metadata' which is declared here on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'

[96msrc/services/ocr/tier2-provider.ts[0m:[93m95[0m:[93m49[0m - [91merror[0m[90m TS2339: [0mProperty 'getGenerativeModel' does not exist on type 'VertexAI'.

[7m95[0m       const generativeModel = this.vertexClient.getGenerativeModel({ model: modelName });
[7m  [0m [91m                                                ~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/tier2-provider.ts[0m:[93m205[0m:[93m49[0m - [91merror[0m[90m TS2339: [0mProperty 'getGenerativeModel' does not exist on type 'VertexAI'.

[7m205[0m       const generativeModel = this.vertexClient.getGenerativeModel({ model: modelName });
[7m   [0m [91m                                                ~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/tier2-provider.ts[0m:[93m280[0m:[93m13[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'modelName' does not exist in type '{ processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; }'.

[7m280[0m             modelName: modelName
[7m   [0m [91m            ~~~~~~~~~[0m

  [96m../../packages/shared-types/dist/index.d.ts[0m:[93m1122[0m:[93m5[0m
    [7m1122[0m     metadata?: {
    [7m    [0m [96m    ~~~~~~~~[0m
    The expected type comes from property 'metadata' which is declared here on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'

[96msrc/services/ocr/tier3-provider.ts[0m:[93m99[0m:[93m45[0m - [91merror[0m[90m TS2339: [0mProperty 'processors' does not exist on type 'IProcessor[]'.

[7m99[0m       const processors = processorsResponse.processors || [];
[7m  [0m [91m                                            ~~~~~~~~~~[0m

[96msrc/services/ocr/tier3-provider.ts[0m:[93m100[0m:[93m47[0m - [91merror[0m[90m TS7006: [0mParameter 'processor' implicitly has an 'any' type.

[7m100[0m       const processorExists = processors.some(processor =>
[7m   [0m [91m                                              ~~~~~~~~~[0m

[96msrc/services/ocr/tier3-provider.ts[0m:[93m292[0m:[93m13[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'averageConfidence' does not exist in type '{ processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; }'.

[7m292[0m             averageConfidence: avgConfidence
[7m   [0m [91m            ~~~~~~~~~~~~~~~~~[0m

  [96m../../packages/shared-types/dist/index.d.ts[0m:[93m1122[0m:[93m5[0m
    [7m1122[0m     metadata?: {
    [7m    [0m [96m    ~~~~~~~~[0m
    The expected type comes from property 'metadata' which is declared here on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'

[96msrc/services/ocr/tier3-provider.ts[0m:[93m313[0m:[93m11[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'averageConfidence' does not exist in type '{ processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; }'.

[7m313[0m           averageConfidence: avgConfidence,
[7m   [0m [91m          ~~~~~~~~~~~~~~~~~[0m

  [96m../../packages/shared-types/dist/index.d.ts[0m:[93m1122[0m:[93m5[0m
    [7m1122[0m     metadata?: {
    [7m    [0m [96m    ~~~~~~~~[0m
    The expected type comes from property 'metadata' which is declared here on type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...'

[96msrc/services/ocr/types.ts[0m:[93m12[0m:[93m3[0m - [91merror[0m[90m TS1205: [0mRe-exporting a type when 'isolatedModules' is enabled requires using 'export type'.

[7m12[0m   StartOcrJobInput,
[7m  [0m [91m  ~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/types.ts[0m:[93m13[0m:[93m3[0m - [91merror[0m[90m TS1205: [0mRe-exporting a type when 'isolatedModules' is enabled requires using 'export type'.

[7m13[0m   InvoiceOcrResult,
[7m  [0m [91m  ~~~~~~~~~~~~~~~~[0m

[96msrc/services/ocr/types.ts[0m:[93m14[0m:[93m3[0m - [91merror[0m[90m TS1205: [0mRe-exporting a type when 'isolatedModules' is enabled requires using 'export type'.

[7m14[0m   OcrJobStatus,
[7m  [0m [91m  ~~~~~~~~~~~~[0m

[96msrc/services/pubsub/invoice-processor.ts[0m:[93m114[0m:[93m52[0m - [91merror[0m[90m TS2339: [0mProperty 'INSTANCE_ID' does not exist on type '{ NODE_ENV: "production" | "development" | "test"; PORT: string; HOST: string; OCR_PROCESSING_MODE: "direct" | "pubsub"; DOCUMENT_AI_LOCATION: string; PUBSUB_INVOICE_PROCESSING_TOPIC: string; ... 12 more ...; XERO_CLIENT_SECRET?: string | undefined; }'.

[7m114[0m         'processingDetails.processorInstance': env.INSTANCE_ID || 'unknown'
[7m   [0m [91m                                                   ~~~~~~~~~~~[0m

[96msrc/tests/e2e/api-router.test.ts[0m:[93m2[0m:[93m10[0m - [91merror[0m[90m TS2724: [0m'"fastify"' has no exported member named 'Fastify'. Did you mean 'fastify'?

[7m2[0m import { Fastify, FastifyInstance } from 'fastify';
[7m [0m [91m         ~~~~~~~[0m

  [96m../../node_modules/.pnpm/fastify@4.29.1/node_modules/fastify/fastify.d.ts[0m:[93m194[0m:[93m16[0m
    [7m194[0m   export const fastify: Fastify
    [7m   [0m [96m               ~~~~~~~[0m
    'fastify' is declared here.

[96msrc/tests/e2e/api-router.test.ts[0m:[93m14[0m:[93m38[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ exists: boolean; data: Mock<UnknownFunction>; }' is not assignable to parameter of type 'never'.

[7m 14[0m     get: jest.fn().mockResolvedValue({
[7m   [0m [91m                                     ~[0m
[7m 15[0m       exists: true,
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m 22[0m       }),
[7m   [0m [91m~~~~~~~~~[0m
[7m 23[0m     }),
[7m   [0m [91m~~~~~[0m

[96msrc/tests/e2e/api-router.test.ts[0m:[93m24[0m:[93m41[0m - [91merror[0m[90m TS2345: [0mArgument of type '{}' is not assignable to parameter of type 'never'.

[7m24[0m     update: jest.fn().mockResolvedValue({}),
[7m  [0m [91m                                        ~~[0m

[96msrc/tests/e2e/api-router.test.ts[0m:[93m25[0m:[93m38[0m - [91merror[0m[90m TS2345: [0mArgument of type '{}' is not assignable to parameter of type 'never'.

[7m25[0m     set: jest.fn().mockResolvedValue({}),
[7m  [0m [91m                                     ~~[0m

[96msrc/tests/e2e/api-router.test.ts[0m:[93m53[0m:[93m49[0m - [91merror[0m[90m TS2345: [0mArgument of type 'string[]' is not assignable to parameter of type 'never'.

[7m53[0m       getSignedUrl: jest.fn().mockResolvedValue(['https://storage.googleapis.com/test-bucket/test-invoice.jpg']),
[7m  [0m [91m                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/api-router.test.ts[0m:[93m60[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type 'MockInstance<(name: string, options?: BucketOptions | undefined) => Bucket> & {} & ((name: string, options?: BucketOptions | undefined) => Bucket)'.
  Type 'Mock<UnknownFunction>' is not assignable to type 'MockInstance<(name: string, options?: BucketOptions | undefined) => Bucket>'.
    The types returned by 'getMockImplementation()' are incompatible between these types.
      Type 'UnknownFunction | undefined' is not assignable to type '((name: string, options?: BucketOptions | undefined) => Bucket) | undefined'.
        Type 'UnknownFunction' is not assignable to type '(name: string, options?: BucketOptions | undefined) => Bucket'.
          Type 'unknown' is not assignable to type 'Bucket'.

[7m60[0m     mockStorage.bucket = jest.fn().mockReturnValue(mockBucket as any);
[7m  [0m [91m    ~~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/api-router.test.ts[0m:[93m64[0m:[93m51[0m - [91merror[0m[90m TS2345: [0mArgument of type '"message-id-123"' is not assignable to parameter of type 'never'.

[7m64[0m       publishMessage: jest.fn().mockResolvedValue('message-id-123'),
[7m  [0m [91m                                                  ~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/api-router.test.ts[0m:[93m67[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type 'MockInstance<(name: string, options?: PublishOptions | undefined) => Topic> & {} & ((name: string, options?: PublishOptions | undefined) => Topic)'.
  Type 'Mock<UnknownFunction>' is not assignable to type 'MockInstance<(name: string, options?: PublishOptions | undefined) => Topic>'.
    The types returned by 'getMockImplementation()' are incompatible between these types.
      Type 'UnknownFunction | undefined' is not assignable to type '((name: string, options?: PublishOptions | undefined) => Topic) | undefined'.
        Type 'UnknownFunction' is not assignable to type '(name: string, options?: PublishOptions | undefined) => Topic'.
          Type 'unknown' is not assignable to type 'Topic'.

[7m67[0m     mockPubSub.topic = jest.fn().mockReturnValue(mockTopic as any);
[7m  [0m [91m    ~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/api-router.test.ts[0m:[93m148[0m:[93m51[0m - [91merror[0m[90m TS2345: [0mArgument of type '"message-id-123"' is not assignable to parameter of type 'never'.

[7m148[0m       publishMessage: jest.fn().mockResolvedValue('message-id-123'),
[7m   [0m [91m                                                  ~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/api-router.test.ts[0m:[93m151[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type 'MockInstance<(name: string, options?: PublishOptions | undefined) => Topic> & {} & ((name: string, options?: PublishOptions | undefined) => Topic)'.
  Type 'Mock<UnknownFunction>' is not assignable to type 'MockInstance<(name: string, options?: PublishOptions | undefined) => Topic>'.
    The types returned by 'getMockImplementation()' are incompatible between these types.
      Type 'UnknownFunction | undefined' is not assignable to type '((name: string, options?: PublishOptions | undefined) => Topic) | undefined'.
        Type 'UnknownFunction' is not assignable to type '(name: string, options?: PublishOptions | undefined) => Topic'.
          Type 'unknown' is not assignable to type 'Topic'.

[7m151[0m     mockPubSub.topic = jest.fn().mockReturnValue(mockTopic as any);
[7m   [0m [91m    ~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/image-quality-check.test.ts[0m:[93m96[0m:[93m32[0m - [91merror[0m[90m TS2339: [0mProperty 'destination' does not exist on type '{}'.

[7m96[0m         if (options && options.destination) {
[7m  [0m [91m                               ~~~~~~~~~~~[0m

[96msrc/tests/e2e/image-quality-check.test.ts[0m:[93m107[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type 'MockInstance<(name: string, options?: BucketOptions | undefined) => Bucket> & {} & ((name: string, options?: BucketOptions | undefined) => Bucket)'.
  Type 'Mock<UnknownFunction>' is not assignable to type 'MockInstance<(name: string, options?: BucketOptions | undefined) => Bucket>'.
    The types returned by 'getMockImplementation()' are incompatible between these types.
      Type 'UnknownFunction | undefined' is not assignable to type '((name: string, options?: BucketOptions | undefined) => Bucket) | undefined'.
        Type 'UnknownFunction' is not assignable to type '(name: string, options?: BucketOptions | undefined) => Bucket'.
          Type 'unknown' is not assignable to type 'Bucket'.

[7m107[0m     mockStorage.bucket = jest.fn().mockReturnValue(mockBucket as any);
[7m   [0m [91m    ~~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m28[0m:[93m38[0m - [91merror[0m[90m TS2345: [0mArgument of type 'any' is not assignable to parameter of type 'never'.

[7m28[0m     get: jest.fn().mockResolvedValue({
[7m  [0m [91m                                     ~[0m
[7m29[0m       exists: true,
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~[0m
[7m30[0m       data: () => ({ PUBSUB_INVOICE_EXTRACTION_COMPLETE_TOPIC: 'test-topic' }),
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m31[0m     } as any), // Cast to any
[7m  [0m [91m~~~~~~~~~~~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m32[0m:[93m38[0m - [91merror[0m[90m TS2345: [0mArgument of type 'any' is not assignable to parameter of type 'never'.

[7m32[0m     set: jest.fn().mockResolvedValue({} as WriteResult as any), // Cast to any
[7m  [0m [91m                                     ~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m33[0m:[93m41[0m - [91merror[0m[90m TS2345: [0mArgument of type 'any' is not assignable to parameter of type 'never'.

[7m33[0m     update: jest.fn().mockResolvedValue({} as WriteResult as any), // Cast to any
[7m  [0m [91m                                        ~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m34[0m:[93m41[0m - [91merror[0m[90m TS2345: [0mArgument of type 'any' is not assignable to parameter of type 'never'.

[7m34[0m     delete: jest.fn().mockResolvedValue({} as WriteResult as any), // Cast to any
[7m  [0m [91m                                        ~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m44[0m:[93m40[0m - [91merror[0m[90m TS2345: [0mArgument of type 'any' is not assignable to parameter of type 'never'.

[7m44[0m       get: jest.fn().mockResolvedValue({
[7m  [0m [91m                                       ~[0m
[7m45[0m         exists: true,
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~~~[0m
[7m46[0m         data: () => ({ lastProcessed: Timestamp.now(), statusHistory: [] }),
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m47[0m       } as any), // Cast to any
[7m  [0m [91m~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m48[0m:[93m40[0m - [91merror[0m[90m TS2345: [0mArgument of type 'any' is not assignable to parameter of type 'never'.

[7m48[0m       set: jest.fn().mockResolvedValue({} as WriteResult as any), // Cast to any
[7m  [0m [91m                                       ~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m49[0m:[93m43[0m - [91merror[0m[90m TS2345: [0mArgument of type 'any' is not assignable to parameter of type 'never'.

[7m49[0m       update: jest.fn().mockResolvedValue({} as WriteResult as any), // Cast to any
[7m  [0m [91m                                          ~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m55[0m:[93m38[0m - [91merror[0m[90m TS2345: [0mArgument of type 'any' is not assignable to parameter of type 'never'.

[7m55[0m     add: jest.fn().mockResolvedValue(mockDoc as unknown as DocumentReference<DocumentData> as any), // Cast to any
[7m  [0m [91m                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m60[0m:[93m38[0m - [91merror[0m[90m TS2345: [0mArgument of type 'any' is not assignable to parameter of type 'never'.

[7m60[0m     get: jest.fn().mockResolvedValue({ docs: [] } as any), // Cast to any
[7m  [0m [91m                                     ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m191[0m:[93m5[0m - [91merror[0m[90m TS2375: [0mType 'MockStorage' is not assignable to type 'import("/Users/<USER>/Documents/GitHub/AiClearBill/apps/api/src/tests/types/test-mocks").MockStorage' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'bucket' are incompatible.
    Type 'Mock<MockBucket>' is not assignable to type '(name?: string | undefined) => MockBucket'.
      Type 'Mock<MockBucket>' provides no match for the signature '(name?: string | undefined): MockBucket'.

[7m191[0m     mockStorage = createMockStorage({ fileContent: testInvoiceBuffer });
[7m   [0m [91m    ~~~~~~~~~~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m251[0m:[93m23[0m - [91merror[0m[90m TS2554: [0mExpected 1 arguments, but got 0.

[7m251[0m     expect(mockPubSub.topic().publishMessage).toHaveBeenCalled();
[7m   [0m [91m                      ~~~~~[0m

  [96msrc/tests/types/test-mocks.d.ts[0m:[93m77[0m:[93m11[0m
    [7m77[0m   topic: (name: string) => MockTopic;
    [7m  [0m [96m          ~~~~~~~~~~~~[0m
    An argument for 'name' was not provided.

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m407[0m:[93m56[0m - [91merror[0m[90m TS2345: [0mArgument of type '{}' is not assignable to parameter of type 'never'.

[7m407[0m       updateInvoiceStatus: jest.fn().mockResolvedValue({}),
[7m   [0m [91m                                                       ~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m412[0m:[93m51[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ success: boolean; metadata?: { processingTimeMs: number; imageQualityScore?: number | undefined; ocrCharacterAccuracy?: number | undefined; } | undefined; confidence?: Record<string, number> | undefined; successfulTier?: OcrTier | undefined; error?: string | undefined; extractedData?: { ...; } | undefined; suggest...' is not assignable to parameter of type 'never'.

[7m412[0m       processInvoice: jest.fn().mockResolvedValue({
[7m   [0m [91m                                                  ~[0m
[7m413[0m         success: true,
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m427[0m         metadata: { processingTimeMs: 1532 },
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m428[0m       } as OcrProcessingResult),
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/e2e/ocr-pipeline.test.ts[0m:[93m469[0m:[93m19[0m - [91merror[0m[90m TS18046: [0m'result' is of type 'unknown'.

[7m469[0m           status: result.success ? 'EXTRACTED' : 'FAILED',
[7m   [0m [91m                  ~~~~~~[0m

[96msrc/tests/fixtures/mock-quality-check.ts[0m:[93m9[0m:[93m14[0m - [91merror[0m[90m TS2741: [0mProperty '[OcrTier.MANUAL]' is missing in type '{ 1: { passed: true; score: number; shouldTryNextTier: false; }; 2: { passed: true; score: number; shouldTryNextTier: false; }; 3: { passed: true; score: number; shouldTryNextTier: false; }; }' but required in type 'Record<OcrTier, QualityCheckResult>'.

[7m9[0m export const mockQualityChecks: Record<OcrTier, QualityCheckResult> = {
[7m [0m [91m             ~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/fixtures/mock-quality-check.ts[0m:[93m29[0m:[93m14[0m - [91merror[0m[90m TS2741: [0mProperty '[OcrTier.MANUAL]' is missing in type '{ 1: { passed: false; score: number; failureReasons: string[]; failedFields: string[]; shouldTryNextTier: true; }; 2: { passed: false; score: number; failureReasons: string[]; failedFields: string[]; shouldTryNextTier: true; }; 3: { ...; }; }' but required in type 'Record<OcrTier, QualityCheckResult>'.

[7m29[0m export const mockFailedQualityChecks: Record<OcrTier, QualityCheckResult> = {
[7m  [0m [91m             ~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/tests/jest.setup.ts[0m:[93m9[0m:[93m55[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ uid: string; email: string; }' is not assignable to parameter of type 'never'.

[7m 9[0m const mockVerifyIdToken = jest.fn().mockResolvedValue({
[7m  [0m [91m                                                      ~[0m
[7m10[0m   uid: 'test-user-id',
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~~~~[0m
[7m11[0m   email: '<EMAIL>',
[7m  [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m12[0m });
[7m  [0m [91m~[0m

[96msrc/tests/jest.setup.ts[0m:[93m27[0m:[93m57[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ response: { candidates: { content: { parts: { text: string; }[]; }; }[]; }; }' is not assignable to parameter of type 'never'.

[7m 27[0m const mockGenerateContent = jest.fn().mockResolvedValue({
[7m   [0m [91m                                                        ~[0m
[7m 28[0m   response: {
[7m   [0m [91m~~~~~~~~~~~~~[0m
[7m...[0m 
[7m 45[0m   }
[7m   [0m [91m~~~[0m
[7m 46[0m });
[7m   [0m [91m~[0m

[96msrc/tests/jest.setup.ts[0m:[93m68[0m:[93m52[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ document: { entities: { type: string; mentionText: string; }[]; pages: { tables: { headerRows: number; bodyRows: number; cells: { text: string; }[]; }[]; }[]; }; }[]' is not assignable to parameter of type 'never'.

[7m 68[0m       processDocument: jest.fn().mockResolvedValue([
[7m   [0m [91m                                                   ~[0m
[7m 69[0m         {
[7m   [0m [91m~~~~~~~~~[0m
[7m...[0m 
[7m 94[0m         }
[7m   [0m [91m~~~~~~~~~[0m
[7m 95[0m       ])
[7m   [0m [91m~~~~~~~[0m

[96msrc/tests/utils/mock-factories.ts[0m:[93m23[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type '() => Promise<[Buffer<ArrayBufferLike>]>'.
  Type 'unknown' is not assignable to type 'Promise<[Buffer<ArrayBufferLike>]>'.

[7m23[0m     download: jest.fn().mockResolvedValue([fileContent]),
[7m  [0m [91m    ~~~~~~~~[0m

  [96msrc/tests/types/test-mocks.d.ts[0m:[93m54[0m:[93m3[0m
    [7m54[0m   download: () => Promise<[Buffer]>;
    [7m  [0m [96m  ~~~~~~~~[0m
    The expected type comes from property 'download' which is declared here on type 'MockFile'

[96msrc/tests/utils/mock-factories.ts[0m:[93m23[0m:[93m43[0m - [91merror[0m[90m TS2345: [0mArgument of type 'Buffer<ArrayBuffer>[]' is not assignable to parameter of type 'never'.

[7m23[0m     download: jest.fn().mockResolvedValue([fileContent]),
[7m  [0m [91m                                          ~~~~~~~~~~~~~[0m

[96msrc/tests/utils/mock-factories.ts[0m:[93m24[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type '(options: any) => Promise<[string]>'.
  Type 'unknown' is not assignable to type 'Promise<[string]>'.

[7m24[0m     getSignedUrl: jest.fn().mockResolvedValue([signedUrl]),
[7m  [0m [91m    ~~~~~~~~~~~~[0m

  [96msrc/tests/types/test-mocks.d.ts[0m:[93m55[0m:[93m3[0m
    [7m55[0m   getSignedUrl: (options: any) => Promise<[string]>;
    [7m  [0m [96m  ~~~~~~~~~~~~[0m
    The expected type comes from property 'getSignedUrl' which is declared here on type 'MockFile'

[96msrc/tests/utils/mock-factories.ts[0m:[93m24[0m:[93m47[0m - [91merror[0m[90m TS2345: [0mArgument of type 'string[]' is not assignable to parameter of type 'never'.

[7m24[0m     getSignedUrl: jest.fn().mockResolvedValue([signedUrl]),
[7m  [0m [91m                                              ~~~~~~~~~~~[0m

[96msrc/tests/utils/mock-factories.ts[0m:[93m25[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type '() => Promise<[boolean]>'.
  Type 'unknown' is not assignable to type 'Promise<[boolean]>'.

[7m25[0m     exists: jest.fn().mockResolvedValue([true]),
[7m  [0m [91m    ~~~~~~[0m

  [96msrc/tests/types/test-mocks.d.ts[0m:[93m56[0m:[93m3[0m
    [7m56[0m   exists: () => Promise<[boolean]>;
    [7m  [0m [96m  ~~~~~~[0m
    The expected type comes from property 'exists' which is declared here on type 'MockFile'

[96msrc/tests/utils/mock-factories.ts[0m:[93m25[0m:[93m41[0m - [91merror[0m[90m TS2345: [0mArgument of type 'boolean[]' is not assignable to parameter of type 'never'.

[7m25[0m     exists: jest.fn().mockResolvedValue([true]),
[7m  [0m [91m                                        ~~~~~~[0m

[96msrc/tests/utils/mock-factories.ts[0m:[93m26[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type '(data: string | Buffer<ArrayBufferLike>) => Promise<void>'.
  Type 'unknown' is not assignable to type 'Promise<void>'.

[7m26[0m     save: jest.fn().mockResolvedValue(undefined)
[7m  [0m [91m    ~~~~[0m

  [96msrc/tests/types/test-mocks.d.ts[0m:[93m57[0m:[93m3[0m
    [7m57[0m   save: (data: Buffer | string) => Promise<void>;
    [7m  [0m [96m  ~~~~[0m
    The expected type comes from property 'save' which is declared here on type 'MockFile'

[96msrc/tests/utils/mock-factories.ts[0m:[93m26[0m:[93m39[0m - [91merror[0m[90m TS2345: [0mArgument of type 'undefined' is not assignable to parameter of type 'never'.

[7m26[0m     save: jest.fn().mockResolvedValue(undefined)
[7m  [0m [91m                                      ~~~~~~~~~[0m

[96msrc/tests/utils/mock-factories.ts[0m:[93m31[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type '(filename: string) => MockFile'.
  Type 'unknown' is not assignable to type 'MockFile'.

[7m31[0m     file: jest.fn().mockReturnValue(mockFile),
[7m  [0m [91m    ~~~~[0m

  [96msrc/tests/types/test-mocks.d.ts[0m:[93m61[0m:[93m3[0m
    [7m61[0m   file: (filename: string) => MockFile;
    [7m  [0m [96m  ~~~~[0m
    The expected type comes from property 'file' which is declared here on type 'MockBucket'

[96msrc/tests/utils/mock-factories.ts[0m:[93m32[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type '(filepath: string, options?: any) => Promise<[File]>'.
  Type 'unknown' is not assignable to type 'Promise<[File]>'.

[7m32[0m     upload: jest.fn().mockResolvedValue([mockFile]),
[7m  [0m [91m    ~~~~~~[0m

  [96msrc/tests/types/test-mocks.d.ts[0m:[93m62[0m:[93m3[0m
    [7m62[0m   upload: (filepath: string, options?: any) => Promise<[GcsFile]>;
    [7m  [0m [96m  ~~~~~~[0m
    The expected type comes from property 'upload' which is declared here on type 'MockBucket'

[96msrc/tests/utils/mock-factories.ts[0m:[93m32[0m:[93m41[0m - [91merror[0m[90m TS2345: [0mArgument of type 'MockFile[]' is not assignable to parameter of type 'never'.

[7m32[0m     upload: jest.fn().mockResolvedValue([mockFile]),
[7m  [0m [91m                                        ~~~~~~~~~~[0m

[96msrc/tests/utils/mock-factories.ts[0m:[93m33[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type '(options?: any) => Promise<[File[]]>'.
  Type 'unknown' is not assignable to type 'Promise<[File[]]>'.

[7m33[0m     getFiles: jest.fn().mockResolvedValue([[mockFile]])
[7m  [0m [91m    ~~~~~~~~[0m

  [96msrc/tests/types/test-mocks.d.ts[0m:[93m63[0m:[93m3[0m
    [7m63[0m   getFiles: (options?: any) => Promise<[GcsFile[]]>;
    [7m  [0m [96m  ~~~~~~~~[0m
    The expected type comes from property 'getFiles' which is declared here on type 'MockBucket'

[96msrc/tests/utils/mock-factories.ts[0m:[93m33[0m:[93m43[0m - [91merror[0m[90m TS2345: [0mArgument of type 'MockFile[][]' is not assignable to parameter of type 'never'.

[7m33[0m     getFiles: jest.fn().mockResolvedValue([[mockFile]])
[7m  [0m [91m                                          ~~~~~~~~~~~~[0m

[96msrc/tests/utils/mock-factories.ts[0m:[93m55[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type '(message: { data: Buffer<ArrayBufferLike>; }) => Promise<string>'.
  Type 'unknown' is not assignable to type 'Promise<string>'.

[7m55[0m     publishMessage: jest.fn().mockResolvedValue(messageId),
[7m  [0m [91m    ~~~~~~~~~~~~~~[0m

  [96msrc/tests/types/test-mocks.d.ts[0m:[93m72[0m:[93m3[0m
    [7m72[0m   publishMessage: (message: { data: Buffer }) => Promise<string>;
    [7m  [0m [96m  ~~~~~~~~~~~~~~[0m
    The expected type comes from property 'publishMessage' which is declared here on type 'MockTopic'

[96msrc/tests/utils/mock-factories.ts[0m:[93m55[0m:[93m49[0m - [91merror[0m[90m TS2345: [0mArgument of type 'string' is not assignable to parameter of type 'never'.

[7m55[0m     publishMessage: jest.fn().mockResolvedValue(messageId),
[7m  [0m [91m                                                ~~~~~~~~~[0m

[96msrc/tests/utils/mock-factories.ts[0m:[93m56[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'Mock<UnknownFunction>' is not assignable to type '(data: Buffer<ArrayBufferLike>) => Promise<string>'.
  Type 'unknown' is not assignable to type 'Promise<string>'.

[7m56[0m     publish: jest.fn().mockResolvedValue(messageId)
[7m  [0m [91m    ~~~~~~~[0m

  [96msrc/tests/types/test-mocks.d.ts[0m:[93m73[0m:[93m3[0m
    [7m73[0m   publish: (data: Buffer) => Promise<string>;
    [7m  [0m [96m  ~~~~~~~[0m
    The expected type comes from property 'publish' which is declared here on type 'MockTopic'

[96msrc/tests/utils/mock-factories.ts[0m:[93m56[0m:[93m42[0m - [91merror[0m[90m TS2345: [0mArgument of type 'string' is not assignable to parameter of type 'never'.

[7m56[0m     publish: jest.fn().mockResolvedValue(messageId)
[7m  [0m [91m                                         ~~~~~~~~~[0m

[96msrc/tests/utils/mock-factories.ts[0m:[93m156[0m:[93m11[0m - [91merror[0m[90m TS2430: [0mInterface 'MockStorage' incorrectly extends interface 'Storage'.
  Types of property 'bucket' are incompatible.
    Type 'Mock<MockBucket>' is not assignable to type '(name: string, options?: BucketOptions | undefined) => Bucket'.
      Type 'Mock<MockBucket>' provides no match for the signature '(name: string, options?: BucketOptions | undefined): Bucket'.

[7m156[0m interface MockStorage extends Storage {
[7m   [0m [91m          ~~~~~~~~~~~[0m

[96msrc/trpc/context.ts[0m:[93m11[0m:[93m23[0m - [91merror[0m[90m TS7023: [0m'createContext' implicitly has return type 'any' because it does not have a return type annotation and is referenced directly or indirectly in one of its return expressions.

[7m11[0m export async function createContext({ req }: CreateFastifyContextOptions) {
[7m  [0m [91m                      ~~~~~~~~~~~~~[0m

[96msrc/trpc/context.ts[0m:[93m16[0m:[93m9[0m - [91merror[0m[90m TS7023: [0m'getUserContext' implicitly has return type 'any' because it does not have a return type annotation and is referenced directly or indirectly in one of its return expressions.

[7m16[0m   const getUserContext = () => {
[7m  [0m [91m        ~~~~~~~~~~~~~~[0m

[96msrc/trpc/context.ts[0m:[93m32[0m:[93m13[0m - [91merror[0m[90m TS2456: [0mType alias 'Context' circularly references itself.

[7m32[0m export type Context = inferAsyncReturnType<typeof createContext>;
[7m  [0m [91m            ~~~~~~~[0m

[96msrc/trpc/index.ts[0m:[93m8[0m:[93m10[0m - [91merror[0m[90m TS2459: [0mModule '"./router"' declares 'router' locally, but it is not exported.

[7m8[0m export { router, publicProcedure, protectedProcedure } from './router';
[7m [0m [91m         ~~~~~~[0m

  [96msrc/trpc/router.ts[0m:[93m1[0m:[93m10[0m
    [7m1[0m import { router } from './trpc';
    [7m [0m [96m         ~~~~~~[0m
    'router' is declared here.

[96msrc/trpc/index.ts[0m:[93m8[0m:[93m18[0m - [91merror[0m[90m TS2305: [0mModule '"./router"' has no exported member 'publicProcedure'.

[7m8[0m export { router, publicProcedure, protectedProcedure } from './router';
[7m [0m [91m                 ~~~~~~~~~~~~~~~[0m

[96msrc/trpc/index.ts[0m:[93m8[0m:[93m35[0m - [91merror[0m[90m TS2305: [0mModule '"./router"' has no exported member 'protectedProcedure'.

[7m8[0m export { router, publicProcedure, protectedProcedure } from './router';
[7m [0m [91m                                  ~~~~~~~~~~~~~~~~~~[0m

[96msrc/trpc/trpc.ts[0m:[93m18[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'path' does not exist on type 'DefaultErrorShape'.

[7m18[0m       path: shape.path,
[7m  [0m [91m                  ~~~~[0m

[96msrc/trpc/trpc.ts[0m:[93m83[0m:[93m22[0m - [91merror[0m[90m TS2559: [0mType 'unique symbol' has no properties in common with type '{ tenantId?: string; }'.

[7m83[0m     const inputObj = input as { tenantId?: string };
[7m  [0m [91m                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/utils/auth-claims.ts[0m:[93m108[0m:[93m35[0m - [91merror[0m[90m TS7053: [0mElement implicitly has an 'any' type because expression of type 'UserRole' can't be used to index type '{ invoice_capturer: string[]; approver: string[]; accountant: string[]; tenant_admin: string[]; system_admin: string[]; }'.
  Property '[UserRole.SNAPPER]' does not exist on type '{ invoice_capturer: string[]; approver: string[]; accountant: string[]; tenant_admin: string[]; system_admin: string[]; }'.

[7m108[0m       updatedClaims.permissions = DEFAULT_ROLE_PERMISSIONS[claims.role] || [];
[7m   [0m [91m                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/workers/__tests__/ocrWorker.test.ts[0m:[93m42[0m:[93m26[0m - [91merror[0m[90m TS2694: [0mNamespace 'global.jest' has no exported member 'MockedClass'.

[7m42[0m (OcrOrchestrator as jest.MockedClass<typeof OcrOrchestrator>).mockImplementation(() => ({
[7m  [0m [91m                         ~~~~~~~~~~~[0m

[96msrc/workers/__tests__/ocrWorker.test.ts[0m:[93m129[0m:[93m56[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ success: boolean; successfulTier: OcrTier; extractedData: { vendorName: string; invoiceNumber: string; totalAmount: number; }; confidence: { vendorName: number; invoiceNumber: number; totalAmount: number; }; }' is not assignable to parameter of type 'never'.

[7m129[0m       mockOrchestratorProcessInvoice.mockResolvedValue({
[7m   [0m [91m                                                       ~[0m
[7m130[0m         success: true,
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m141[0m         }
[7m   [0m [91m~~~~~~~~~[0m
[7m142[0m       });
[7m   [0m [91m~~~~~~~[0m

[96msrc/workers/__tests__/ocrWorker.test.ts[0m:[93m194[0m:[93m56[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ success: boolean; error: string; }' is not assignable to parameter of type 'never'.

[7m194[0m       mockOrchestratorProcessInvoice.mockResolvedValue({
[7m   [0m [91m                                                       ~[0m
[7m195[0m         success: false,
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m196[0m         error: 'All OCR tiers failed'
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m197[0m       });
[7m   [0m [91m~~~~~~~[0m

[96msrc/workers/__tests__/ocrWorker.test.ts[0m:[93m221[0m:[93m56[0m - [91merror[0m[90m TS2345: [0mArgument of type 'Error' is not assignable to parameter of type 'never'.

[7m221[0m       mockOrchestratorProcessInvoice.mockRejectedValue(new Error('Network timeout'));
[7m   [0m [91m                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/workers/ocrWorker.ts[0m:[93m30[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'flowControlSettings' does not exist in type 'SubscriberOptions'.

[7m30[0m       flowControlSettings: {
[7m  [0m [91m      ~~~~~~~~~~~~~~~~~~~[0m


Found 270 errors in 51 files.

Errors  Files
     1  src/auth/permissions.ts[90m:8[0m
    11  src/config/firebase-test.ts[90m:5[0m
     1  src/config/firebase.ts[90m:38[0m
     1  src/config/logger.ts[90m:7[0m
     2  src/index.ts[90m:50[0m
     3  src/middleware/auth.ts[90m:11[0m
    15  src/routes/accounting.ts[90m:54[0m
     1  src/routes/customers.router.ts[90m:97[0m
     6  src/routes/invoices.router.ts[90m:142[0m
     3  src/routes/ocr.router.ts[90m:331[0m
     3  src/routes/tenants.router.ts[90m:104[0m
     4  src/routes/vendors.router.ts[90m:64[0m
     8  src/routes/webhooks.router.ts[90m:12[0m
     1  src/services/accounting/kmsEncryption.ts[90m:8[0m
     2  src/services/accounting/monitoredProvider.ts[90m:54[0m
     9  src/services/accounting/monitoring.ts[90m:42[0m
     4  src/services/accounting/push/pushWorker.ts[90m:255[0m
     1  src/services/accounting/push/taskClient.ts[90m:7[0m
     3  src/services/accounting/qbo/index.ts[90m:168[0m
     2  src/services/accounting/qbo/mapper.ts[90m:70[0m
     1  src/services/accounting/qbo/sdk.ts[90m:6[0m
     8  src/services/accounting/status/transactionStatus.ts[90m:176[0m
    19  src/services/accounting/sync/deltaSync.ts[90m:191[0m
     2  src/services/accounting/sync/nightlySync.ts[90m:8[0m
     1  src/services/accounting/sync/webhookListener.ts[90m:9[0m
    14  src/services/accounting/webhooks/handlers.ts[90m:151[0m
     3  src/services/accounting/webhooks/registration.ts[90m:42[0m
     4  src/services/ocr/__tests__/integration.test.ts[90m:112[0m
    14  src/services/ocr/__tests__/orchestrator.test.ts[90m:40[0m
     3  src/services/ocr/__tests__/simple-integration.test.ts[90m:118[0m
     2  src/services/ocr/__tests__/tier1-provider.test.ts[90m:10[0m
    21  src/services/ocr/__tests__/tier2-provider.test.ts[90m:33[0m
    14  src/services/ocr/__tests__/tier3-provider.test.ts[90m:10[0m
     5  src/services/ocr/quality-check.ts[90m:364[0m
     2  src/services/ocr/tier1-provider.ts[90m:25[0m
     5  src/services/ocr/tier2-provider.ts[90m:30[0m
     4  src/services/ocr/tier3-provider.ts[90m:99[0m
     3  src/services/ocr/types.ts[90m:12[0m
     1  src/services/pubsub/invoice-processor.ts[90m:114[0m
    10  src/tests/e2e/api-router.test.ts[90m:2[0m
     2  src/tests/e2e/image-quality-check.test.ts[90m:96[0m
    14  src/tests/e2e/ocr-pipeline.test.ts[90m:28[0m
     2  src/tests/fixtures/mock-quality-check.ts[90m:9[0m
     3  src/tests/jest.setup.ts[90m:9[0m
    18  src/tests/utils/mock-factories.ts[90m:23[0m
     3  src/trpc/context.ts[90m:11[0m
     3  src/trpc/index.ts[90m:8[0m
     2  src/trpc/trpc.ts[90m:18[0m
     1  src/utils/auth-claims.ts[90m:108[0m
     4  src/workers/__tests__/ocrWorker.test.ts[90m:42[0m
     1  src/workers/ocrWorker.ts[90m:30[0m
[41m[30m ELIFECYCLE [39m[49m [31mCommand failed with exit code 2.[39m
