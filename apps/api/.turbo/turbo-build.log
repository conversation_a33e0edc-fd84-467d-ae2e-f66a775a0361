

> @billsnapp/api@0.0.0 build /Users/<USER>/Documents/GitHub/AiClearBill/apps/api
> tsup

[34mCLI[39m Building entry: src/index.ts
[34mCLI[39m Using tsconfig: tsconfig.json
[34mCLI[39m tsup v8.4.0
[34mCLI[39m Using tsup config: /Users/<USER>/Documents/GitHub/AiClearBill/apps/api/tsup.config.ts
[34mCLI[39m Target: node18
[34mCLI[39m Cleaning output folder
[34mESM[39m Build start
[32mESM[39m [1mdist/index.js     [22m[32m118.81 KB[39m
[32mESM[39m [1mdist/index.js.map [22m[32m233.01 KB[39m
[32mESM[39m ⚡️ Build success in 25ms
