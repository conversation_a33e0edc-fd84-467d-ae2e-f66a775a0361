import { initializeApp, cert, type ServiceAccount } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { logger } from './logger.js';

// ESM-compatible __dirname equivalent
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Firebase Admin
const initializeFirebase = async () => {
  try {
    // Skip real Firebase initialization in test environment
    if (process.env.NODE_ENV === 'test') {
      logger.info('Test environment detected - using mock Firebase');
      // Return mock Firebase objects for testing
      const { firebase } = await import('./firebase-test.js');
      return firebase;
    }
    
    // Check if running in GCP environment (Production)
    if (process.env.NODE_ENV === 'production' && process.env.GOOGLE_APPLICATION_CREDENTIALS) {
      // In Cloud Run, we use the default credentials
      logger.info('Initializing Firebase with default credentials');
      initializeApp();
    } else {
      // Local development - use environment variables or service account file

      // First, try to use environment variables (temporarily disabled for JSON file testing)
      if (false && process.env.FIREBASE_PRIVATE_KEY && process.env.FIREBASE_CLIENT_EMAIL && process.env.FIREBASE_PROJECT_ID) {
        logger.info('Initializing Firebase with environment variables');

        // Process the private key to handle newlines correctly
        let privateKey = process.env.FIREBASE_PRIVATE_KEY;

        // If the private key doesn't start with -----BEGIN, it might be base64 encoded or malformed
        if (!privateKey.startsWith('-----BEGIN')) {
          logger.warn('Private key does not start with -----BEGIN, attempting to fix formatting');
        }

        // Replace escaped newlines with actual newlines
        privateKey = privateKey.replace(/\\n/g, '\n');

        // If the private key is a single line (no newlines), format it properly
        if (!privateKey.includes('\n')) {
          logger.info('Private key appears to be single-line, formatting for PEM standard');

          // Extract the header, body, and footer
          const beginMatch = privateKey.match(/^-----BEGIN PRIVATE KEY-----(.+)-----END PRIVATE KEY-----$/);
          if (beginMatch) {
            const keyBody = beginMatch[1];

            // Split the key body into 64-character lines
            const lines = [];
            for (let i = 0; i < keyBody.length; i += 64) {
              lines.push(keyBody.substring(i, i + 64));
            }

            // Reconstruct the properly formatted key
            privateKey = '-----BEGIN PRIVATE KEY-----\n' + lines.join('\n') + '\n-----END PRIVATE KEY-----';
          }
        }

        // Log the first and last few characters for debugging (without exposing the key)
        logger.debug(`Private key format check: starts with "${privateKey.substring(0, 15)}", ends with "${privateKey.substring(privateKey.length - 15)}"`);

        const serviceAccount: ServiceAccount = {
          type: 'service_account',
          project_id: process.env.FIREBASE_PROJECT_ID,
          private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
          private_key: privateKey,
          client_email: process.env.FIREBASE_CLIENT_EMAIL,
          client_id: process.env.FIREBASE_CLIENT_ID,
          auth_uri: process.env.FIREBASE_AUTH_URI || 'https://accounts.google.com/o/oauth2/auth',
          token_uri: process.env.FIREBASE_TOKEN_URI || 'https://oauth2.googleapis.com/token',
          auth_provider_x509_cert_url: process.env.FIREBASE_AUTH_PROVIDER_X509_CERT_URL || 'https://www.googleapis.com/oauth2/v1/certs',
          client_x509_cert_url: process.env.FIREBASE_CLIENT_X509_CERT_URL,
        };

        try {
          initializeApp({
            credential: cert(serviceAccount),
            storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
          });
        } catch (credentialError) {
          logger.error('Failed to initialize Firebase with environment variables, trying alternative approach:', credentialError);

          // Alternative: Create a temporary JSON file
          const tempServiceAccountPath = path.join(process.cwd(), 'temp-firebase-credentials.json');
          try {
            fs.writeFileSync(tempServiceAccountPath, JSON.stringify(serviceAccount, null, 2));
            logger.info('Created temporary service account file, retrying Firebase initialization');

            const tempServiceAccount = JSON.parse(fs.readFileSync(tempServiceAccountPath, 'utf8')) as ServiceAccount;
            initializeApp({
              credential: cert(tempServiceAccount),
              storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
            });

            // Clean up temp file
            fs.unlinkSync(tempServiceAccountPath);
            logger.info('Firebase initialized successfully with temporary file approach');
          } catch (tempError) {
            // Clean up temp file if it exists
            if (fs.existsSync(tempServiceAccountPath)) {
              fs.unlinkSync(tempServiceAccountPath);
            }
            throw credentialError; // Throw the original error
          }
        }
      } else {
        // Fallback to service account file
        const possiblePaths = [
          process.env.FIREBASE_SERVICE_ACCOUNT_PATH,
          path.join(process.cwd(), 'serviceAccountKey.json'),
          path.join(process.cwd(), '../../serviceAccountKey.json'), // From monorepo root
          path.join(process.cwd(), 'firebase-credentials.json'),
        ].filter(Boolean);

        let serviceAccountPath: string | null = null;
        for (const testPath of possiblePaths) {
          if (fs.existsSync(testPath!)) {
            serviceAccountPath = testPath!;
            break;
          }
        }

        if (!serviceAccountPath) {
          throw new Error(`Firebase service account file not found. Tried paths: ${possiblePaths.join(', ')}. Please provide either FIREBASE_PRIVATE_KEY and FIREBASE_CLIENT_EMAIL environment variables, or a service account JSON file.`);
        }

        logger.info(`Initializing Firebase with service account file: ${serviceAccountPath}`);
        const serviceAccount = JSON.parse(
          fs.readFileSync(serviceAccountPath, 'utf8')
        ) as ServiceAccount;

        initializeApp({
          credential: cert(serviceAccount),
          storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
        });
      }
    }
    
    // Initialize auth, firestore, and storage
    const auth = getAuth();
    const firestore = getFirestore();
    const storage = getStorage();
    
    // Set Firestore settings
    firestore.settings({
      ignoreUndefinedProperties: true,
    });
    
    logger.info('Firebase initialization successful');
    return { auth, firestore, storage };
  } catch (error) {
    logger.error('Firebase initialization failed:', error);
    throw error;
  }
};

export const firebase = await initializeFirebase();
