import { z } from 'zod';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { logger } from './logger.js';

// ESM-compatible __dirname equivalent
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env file
// Look for .env in the monorepo root (two levels up from this file)
const envPath = path.resolve(__dirname, '../../../.env');
dotenv.config({ path: envPath });

// Define the environment schema with Zod
const envSchema = z.object({
  // Server configuration
  PORT: z.string().default('4000'),
  HOST: z.string().default('0.0.0.0'),
  NODE_ENV: z.enum(['development', 'test', 'production']).default('development'),
  
  // Firebase configuration
  FIREBASE_SERVICE_ACCOUNT_PATH: z.string().optional(),
  FIREBASE_STORAGE_BUCKET: z.string().optional(),
  FIREBASE_PROJECT_ID: z.string().optional(),
  FIREBASE_PRIVATE_KEY_ID: z.string().optional(),
  FIREBASE_PRIVATE_KEY: z.string().optional(),
  FIREBASE_CLIENT_EMAIL: z.string().optional(),
  FIREBASE_CLIENT_ID: z.string().optional(),
  FIREBASE_AUTH_URI: z.string().optional(),
  FIREBASE_TOKEN_URI: z.string().optional(),
  FIREBASE_AUTH_PROVIDER_X509_CERT_URL: z.string().optional(),
  FIREBASE_CLIENT_X509_CERT_URL: z.string().optional(),
  FIREBASE_DATABASE_URL: z.string().optional(),
  
  // Google Cloud configuration
  GOOGLE_APPLICATION_CREDENTIALS: z.string().optional(),
  GOOGLE_CLOUD_PROJECT: z.string().optional(),
  
  // OCR configuration
  GEMMA_OCR_API_KEY: z.string().optional(),
  GEMMA_OCR_ENDPOINT: z.string().optional(),
  OCR_PROCESSING_MODE: z.enum(['direct', 'pubsub']).default('pubsub'),

  // Document AI configuration
  DOCUMENT_AI_INVOICE_PROCESSOR_ID: z.string().optional(),
  DOCUMENT_AI_LOCATION: z.string().default('us-central1'),

  // Pub/Sub topics
  PUBSUB_INVOICE_PROCESSING_TOPIC: z.string().default('invoice-processing'),
  PUBSUB_INVOICE_PROCESSING_SUBSCRIPTION: z.string().default('invoice-processing-subscription'),
  PUBSUB_INVOICE_EXTRACTION_COMPLETE_TOPIC: z.string().default('invoice-extraction-complete'),
  
  // API keys and secrets
  QBO_CLIENT_ID: z.string().optional(),
  QBO_CLIENT_SECRET: z.string().optional(),
  XERO_CLIENT_ID: z.string().optional(),
  XERO_CLIENT_SECRET: z.string().optional()
});

// Parse environment variables with validation
const parseEnv = () => {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors
        .filter(e => e.code === 'invalid_type' && e.received === 'undefined')
        .map(e => e.path.join('.'));
      
      if (missingVars.length > 0) {
        logger.error(`Missing required environment variables: ${missingVars.join(', ')}`);
      }
      
      logger.error('Environment validation failed:', error.errors);
    } else {
      logger.error('Unknown error during environment validation:', error);
    }
    
    throw new Error('Environment validation failed');
  }
};

// Export validated environment variables
export const env = parseEnv();
