import fastify from 'fastify';
import cors from '@fastify/cors';
import { fastifyTRPCPlugin } from '@trpc/server/adapters/fastify';
import { env } from './config/env';
import { logger } from './config/logger';
import { createContext } from './trpc/context';
import { appRouter } from './trpc/router';

/**
 * Create and configure the Fastify server
 */
const startServer = async () => {
  const server = fastify({
    logger: env.NODE_ENV === 'development',
    maxParamLength: 5000, // For handling long query parameters
  });

  // Register CORS plugin
  await server.register(cors, {
    origin: (origin, cb) => {
      // In development, allow all origins
      if (env.NODE_ENV === 'development') {
        cb(null, true);
        return;
      }

      // In production, restrict to allowed origins
      const allowedOrigins = [
        process.env.FRONTEND_URL || 'https://app.billsnapp.com',
        // Add any other allowed origins here
      ];

      if (!origin || allowedOrigins.includes(origin)) {
        cb(null, true);
      } else {
        cb(new Error(`Origin ${origin} not allowed`), false);
      }
    },
    credentials: true,
  });

  // Register tRPC plugin
  await server.register(fastifyTRPCPlugin, {
    prefix: '/trpc',
    trpcOptions: {
      router: appRouter,
      createContext,
      onError:
        env.NODE_ENV === 'development'
          ? ({ path, error }) => {
              logger.error(`Error on ${path ?? '<no-path>'}:`, error);
            }
          : undefined,
    },
  });

  // Add a health check endpoint
  server.get('/health', async () => {
    return { status: 'ok', timestamp: new Date().toISOString() };
  });

  // Define server address and port
  const host = env.HOST || '0.0.0.0';
  const port = parseInt(env.PORT || '8080', 10);

  try {
    // Start the server
    await server.listen({ host, port });
    logger.info(`Server listening on ${host}:${port}`);
  } catch (error) {
    logger.error('Error starting server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();
