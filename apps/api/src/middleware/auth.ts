import { TRPCError } from '@trpc/server';
import { UserRole } from '@billsnapp/shared-types';
import { getFirebase } from '../config/firebase';
import { logger } from '../config/logger';
import { type Context } from '../trpc/context';

/**
 * Middleware to verify Firebase authentication token
 * Adds user information to the context if token is valid
 */
export const verifyAuth = async (token: string | undefined): Promise<Context['user']> => {
  if (!token) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication token is required',
    });
  }

  try {
    // Remove 'Bearer ' prefix if present
    const cleanToken = token.startsWith('Bearer ') ? token.slice(7) : token;

    // Get Firebase instance
    const firebase = await getFirebase();

    // Verify the token with Firebase Auth
    const decodedToken = await firebase.auth.verifyIdToken(cleanToken);

    // Get the user record from Firebase Auth
    const userRecord = await firebase.auth.getUser(decodedToken.uid);

    // Get user profile from Firestore to include role and tenant information
    const userDoc = await firebase.firestore
      .collection('users')
      .doc(decodedToken.uid)
      .get();
    
    if (!userDoc.exists) {
      logger.warn(`User document not found for uid: ${decodedToken.uid}`);
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'User profile not found',
      });
    }
    
    const userData = userDoc.data();
    
    // Check if user is active
    if (userData?.status !== 'active') {
      logger.warn(`Inactive user attempted access: ${decodedToken.uid}`);
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'User account is inactive or suspended',
      });
    }
    
    // Return user context
    return {
      uid: decodedToken.uid,
      email: userRecord.email || '',
      displayName: userRecord.displayName || '',
      role: userData?.role as UserRole,
      tenantId: userData?.tenantId as string,
    };
  } catch (error) {
    logger.error('Token verification failed:', error);
    
    // Return specific error based on the Firebase error
    if (error instanceof TRPCError) {
      throw error;
    }
    
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Invalid or expired authentication token',
    });
  }
};

/**
 * Middleware to check if the user has the required role
 */
export const requireRole = (
  user: Context['user'] | null, 
  requiredRole: UserRole
): void => {
  if (!user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED', 
      message: 'Authentication required'
    });
  }
  
  const roleLevel = {
    [UserRole.INVOICE_CAPTURER]: 1,
    [UserRole.APPROVER]: 2,
    [UserRole.ACCOUNTANT]: 3,
    [UserRole.TENANT_ADMIN]: 4,
    [UserRole.SYSTEM_ADMIN]: 5,
  };
  
  if (roleLevel[user.role] < roleLevel[requiredRole]) {
    logger.warn(`User ${user.uid} with role ${user.role} attempted to access route requiring ${requiredRole}`);
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Insufficient permissions',
    });
  }
};

/**
 * Middleware to ensure the user belongs to the specified tenant
 * System admins are exempt from this check
 */
export const validateTenantAccess = (
  user: Context['user'] | null,
  tenantId: string
): void => {
  if (!user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    });
  }
  
  // System admins can access any tenant
  if (user.role === UserRole.SYSTEM_ADMIN) {
    return;
  }
  
  // Check if user belongs to the requested tenant
  if (user.tenantId !== tenantId) {
    logger.warn(`Cross-tenant access attempt: User ${user.uid} (tenant: ${user.tenantId}) tried to access tenant ${tenantId}`);
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'You do not have access to this organization',
    });
  }
};
