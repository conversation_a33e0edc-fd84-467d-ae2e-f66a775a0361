/**
 * Utilities for managing Firebase Auth custom claims
 * Implements the Billsnapp role-based access control system
 */
import { getFirebase } from '../config/firebase';
import { logger } from '../config/logger';
import { UserRole } from '@billsnapp/shared-types';

/**
 * Maximum size for custom claims (Firebase limit)
 * The total custom claims payload must be less than 1000 bytes
 */
const MAX_CLAIMS_SIZE = 1000;

/**
 * Generate an email alias for a user in a tenant
 * Format: <tenant>.<user>@billsnap.ai
 */
export const generateEmailAlias = (
  tenantId: string,
  uid: string,
  tenantName?: string
): string => {
  // Create a safe tenant name segment (remove spaces, special chars)
  const safePrefix = (tenantName || tenantId).toLowerCase()
    .replace(/[^a-z0-9]/g, '');
  
  // Take first 12 chars of tenant name/id to keep alias reasonably sized
  const tenantPrefix = safePrefix.slice(0, 12);
  
  // Take a portion of the user ID to ensure uniqueness
  const userSuffix = uid.slice(0, 8);
  
  return `${tenantPrefix}.${userSuffix}@billsnap.ai`;
};

/**
 * Default permissions for each Billsnapp role
 */
export const DEFAULT_ROLE_PERMISSIONS = {
  // Snapper - can only capture and view their own invoices
  [UserRole.INVOICE_CAPTURER]: [
    'view:invoice',
    'create:invoice'
  ],
  
  // Checker - can edit and approve but not push to QuickBooks
  [UserRole.APPROVER]: [
    'view:invoice',
    'create:invoice',
    'edit:invoice',
    'view:dashboard',
    'view:vendor',
    'view:account'
  ],
  
  // Builder-in-Chief - full access including push to QuickBooks
  [UserRole.ACCOUNTANT]: [
    'view:invoice',
    'create:invoice',
    'edit:invoice',
    'delete:invoice', 
    'push:invoice',
    'approve:invoice',
    'view:dashboard',
    'create:vendor',
    'edit:vendor',
    'view:account'
  ],
  
  // Administrators - full system access
  [UserRole.TENANT_ADMIN]: [
    'admin:tenant'
  ],
  
  [UserRole.SYSTEM_ADMIN]: [
    'admin:system'
  ]
};

/**
 * Set custom claims for a user in Firebase Auth
 * @param uid - User ID
 * @param claims - Object containing claims to set
 */
export const setCustomClaims = async (
  uid: string,
  claims: {
    role?: UserRole;
    tenantId?: string;
    emailAlias?: string;
    permissions?: string[];
    pushLimit?: number;
  }
): Promise<void> => {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();

    // Get current user claims
    const { customClaims } = await firebase.auth.getUser(uid);
    
    // Merge with existing claims
    const updatedClaims = {
      ...customClaims,
      ...claims
    };
    
    // For roles that should have default permissions, replace existing permissions
    if (claims.role && !claims.permissions) {
      updatedClaims.permissions = DEFAULT_ROLE_PERMISSIONS[claims.role] || [];
    }
    
    // Add emailAlias if not present but we have tenantId
    if (!updatedClaims.emailAlias && updatedClaims.tenantId) {
      updatedClaims.emailAlias = generateEmailAlias(updatedClaims.tenantId, uid);
    }
    
    // Ensure claims size is under Firebase limit
    const claimsSize = Buffer.from(JSON.stringify(updatedClaims)).length;
    if (claimsSize > MAX_CLAIMS_SIZE) {
      logger.warn(`Claims payload for user ${uid} is too large: ${claimsSize} bytes`);
      // If permissions array is making it too large, only keep essential claims
      if (updatedClaims.permissions && updatedClaims.permissions.length > 10) {
        updatedClaims.permissions = updatedClaims.permissions.slice(0, 10);
        logger.warn(`Truncated permissions array for user ${uid} to stay under size limit`);
      }
    }
    
    // Set the custom claims
    await firebase.auth.setCustomUserClaims(uid, updatedClaims);
    logger.info(`Updated custom claims for user ${uid}`);
    
    return;
  } catch (error) {
    logger.error('Error setting custom claims:', error);
    throw error;
  }
};

/**
 * Map the Billsnapp frontend roles to API UserRole enum
 */
export const mapBillsnappRoleToApi = (role: string): UserRole => {
  switch (role) {
    case 'snapper':
      return UserRole.INVOICE_CAPTURER;
    case 'checker':
      return UserRole.APPROVER;
    case 'approver':
      return UserRole.ACCOUNTANT;
    default:
      return UserRole.INVOICE_CAPTURER; // Default to most restricted role
  }
};

/**
 * Map API UserRole enum to Billsnapp frontend roles
 */
export const mapApiToBillsnappRole = (role: UserRole): string => {
  switch (role) {
    case UserRole.INVOICE_CAPTURER:
      return 'snapper';
    case UserRole.APPROVER:
      return 'checker';
    case UserRole.ACCOUNTANT:
      return 'approver';
    case UserRole.TENANT_ADMIN:
      return 'admin';
    case UserRole.SYSTEM_ADMIN:
      return 'super_admin';
    default:
      return 'snapper';
  }
};
