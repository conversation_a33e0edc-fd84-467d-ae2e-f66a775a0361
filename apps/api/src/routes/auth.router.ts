import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, publicProcedure, authProcedure } from '../trpc/trpc';
import { getFirebase } from '../config/firebase';
import { logger } from '../config/logger';

/**
 * Authentication router for handling user authentication operations
 */
export const authRouter = router({
  /**
   * Get the current authenticated user profile
   */
  getUser: authProcedure
    .input(z.object({
      uid: z.string().optional()
    }))
    .query(async ({ ctx, input }) => {
      try {
        // If UID provided and different from authenticated user, check permissions
        // Only admins can fetch other users
        const targetUid = input.uid || ctx.user.uid;
        
        if (input.uid && input.uid !== ctx.user.uid && 
           !['tenant_admin', 'system_admin'].includes(ctx.user.role)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to view this user'
          });
        }

        // Get Firebase instance
        const firebase = await getFirebase();

        // Get user document from Firestore
        const userDoc = await firebase.firestore
          .collection('users')
          .doc(targetUid)
          .get();
        
        if (!userDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found'
          });
        }
        
        return userDoc.data();
      } catch (error) {
        logger.error('Error getting user:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to retrieve user details'
        });
      }
    }),
  
  /**
   * Verify a Firebase token
   * Used primarily for testing and debugging
   */
  verifyToken: publicProcedure
    .input(z.object({
      token: z.string()
    }))
    .mutation(async ({ input }) => {
      try {
        // Get Firebase instance
        const firebase = await getFirebase();

        const decodedToken = await firebase.auth.verifyIdToken(input.token);
        return { 
          valid: true, 
          uid: decodedToken.uid,
          email: decodedToken.email
        };
      } catch (error) {
        logger.warn('Token verification failed:', error);
        
        return { 
          valid: false, 
          error: 'Invalid or expired token' 
        };
      }
    })
});
