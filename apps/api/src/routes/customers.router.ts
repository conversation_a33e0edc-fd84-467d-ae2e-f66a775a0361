import { z } from 'zod';
import { router, authProcedure } from '../trpc/trpc';
import { TRPCError } from '@trpc/server';
import { getFirebase } from '../config/firebase';
import { logger } from '../config/logger';
import { getAccountingProvider } from '../services/accounting/factory';
import { CustomerInput } from '../services/accounting/types';

// Customer creation schema
const customerInputSchema = z.object({
  name: z.string().min(1, 'Customer name is required'),
  email: z.string().email('Invalid email').optional().nullable(),
  phone: z.string().optional().nullable(),
  taxId: z.string().optional().nullable(),
  address: z.object({
    line1: z.string().optional().nullable(),
    line2: z.string().optional().nullable(),
    city: z.string().optional().nullable(),
    region: z.string().optional().nullable(),
    postalCode: z.string().optional().nullable(),
    country: z.string().optional().nullable()
  }).optional(),
  active: z.boolean().default(true),
  pushToAccounting: z.boolean().default(false)
});

/**
 * Router for customer management operations
 */
export const customersRouter = router({
  /**
   * Create a new customer for the current tenant
   * Optionally push to the connected accounting provider
   */
  createCustomer: authProcedure
    .input(
      z.object({
        tenantId: z.string(),
        customer: customerInputSchema,
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Verify user has access to this tenant
      await ctx.validateTenantAccess(input.tenantId);
      
      try {
        const { tenantId, customer } = input;
        const { pushToAccounting, ...customerData } = customer;

        // Get Firebase instance
        const firebase = await getFirebase();

        // Create default searchable fields
        const nameSearchable = customer.name.toLowerCase();

        // Create the customer document in Firestore
        const customerRef = firebase.firestore.collection('customers').doc();
        const customerId = customerRef.id;
        
        const timestamp = firebase.firestore.FieldValue.serverTimestamp();
        
        // Clean customer data for storage
        const cleanCustomerData = {
          ...customerData,
          email: customerData.email || null,
          phone: customerData.phone || null,
          taxId: customerData.taxId || null,
          address: customerData.address || {
            line1: null,
            line2: null,
            city: null,
            region: null,
            postalCode: null,
            country: null
          },
          tenantId,
          nameSearchable,
          createdAt: timestamp,
          updatedAt: timestamp,
          createdBy: ctx.user.uid,
          updatedBy: ctx.user.uid,
          providerId: null, // Will be updated if push succeeds
        };
        
        // Save customer to Firestore
        await customerRef.set(cleanCustomerData);
        
        // If we should push to accounting system
        let pushSuccess = false;
        let providerId = null;
        
        if (pushToAccounting) {
          try {
            // Map to accounting provider input format
            const customerInput: CustomerInput = {
              name: customer.name,
              email: customer.email || undefined,
              phone: customer.phone || undefined,
              taxId: customer.taxId || undefined,
              address: customer.address,
              active: customer.active
            };
            
            // Get the accounting provider for this tenant
            const provider = getAccountingProvider(tenantId, 'qbo');
            
            // Push customer to accounting system
            providerId = await provider.createCustomer(customerInput);
            
            // Update customer document with provider ID
            if (providerId) {
              await customerRef.update({
                providerId,
                providerIds: { qbo: providerId },
                lastPushedAt: timestamp
              });
              
              pushSuccess = true;
            }
          } catch (err) {
            // Log the error but don't fail the customer creation
            logger.error('Failed to push customer to accounting system', {
              error: err,
              tenantId,
              customerId
            });
          }
        }
        
        return {
          id: customerId,
          providerId,
          pushSuccess
        };
      } catch (error) {
        logger.error('Failed to create customer', { error });
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to create customer: ${(error as Error).message}`,
        });
      }
    }),
    
  /**
   * Get a list of customers for the current tenant
   */
  getCustomers: authProcedure
    .input(
      z.object({
        tenantId: z.string(),
        active: z.boolean().optional(),
        limit: z.number().min(1).max(100).optional().default(50),
        cursor: z.string().optional(),
        search: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      // Verify that the user has access to this tenant
      await ctx.validateTenantAccess(input.tenantId);

      try {
        // Get Firebase instance
        const firebase = await getFirebase();

        // Start building the query
        let customersQuery = firebase.firestore
          .collection('customers')
          .where('tenantId', '==', input.tenantId)
          .orderBy('name');

        // Apply active filter if specified
        if (input.active !== undefined) {
          customersQuery = customersQuery.where('active', '==', input.active);
        }

        // Apply search filter if specified
        if (input.search) {
          const searchLower = input.search.toLowerCase();
          // Firestore doesn't support text search, so we'll filter in-memory
          customersQuery = customersQuery.where('nameSearchable', '>=', searchLower)
            .where('nameSearchable', '<=', searchLower + '\uf8ff');
        }

        // Apply pagination
        if (input.cursor) {
          const cursorDoc = await firebase.firestore.collection('customers').doc(input.cursor).get();
          if (!cursorDoc.exists) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Cursor document not found',
            });
          }
          customersQuery = customersQuery.startAfter(cursorDoc);
        }

        // Limit results
        customersQuery = customersQuery.limit(input.limit);

        // Execute query
        const snapshot = await customersQuery.get();

        // Transform results
        const customers = [];
        let lastCursor = null;

        for (const doc of snapshot.docs) {
          customers.push({
            id: doc.id,
            ...doc.data(),
            createdAt: doc.data().createdAt?.toDate().toISOString(),
            updatedAt: doc.data().updatedAt?.toDate().toISOString(),
            lastPushedAt: doc.data().lastPushedAt?.toDate().toISOString(),
          });
          lastCursor = doc.id;
        }

        return {
          customers,
          nextCursor: customers.length === input.limit ? lastCursor : null,
        };
      } catch (error) {
        logger.error('Failed to get customers', { error });
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to get customers: ${(error as Error).message}`,
        });
      }
    })
});
