import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, authProcedure, approverProcedure } from '../trpc/trpc';
import { getFirebase } from '../config/firebase';
import { logger } from '../config/logger';
import { InvoiceStatus, OcrTier } from '@billsnapp/shared-types';
import { PubSub } from '@google-cloud/pubsub';
import { env } from '../config/env';

// Initialize PubSub client for invoice processing
const pubsub = new PubSub();

/**
 * Invoices router for managing invoice processing in the system
 */
export const invoicesRouter = router({
  /**
   * Get a single invoice by ID
   * Users can only view invoices in their tenant
   */
  getInvoice: authProcedure
    .input(z.object({
      invoiceId: z.string()
    }))
    .query(async ({ ctx, input }) => {
      try {
        // Get Firebase instance
        const firebase = await getFirebase();

        // Get the invoice document
        const invoiceDoc = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .get();
        
        if (!invoiceDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invoice not found'
          });
        }
        
        const invoiceData = invoiceDoc.data();
        
        // Check tenant access
        if (invoiceData?.tenantId !== ctx.user.tenantId && ctx.user.role !== 'system_admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to view this invoice'
          });
        }
        
        return {
          id: invoiceDoc.id,
          ...invoiceData
        };
      } catch (error) {
        logger.error('Error getting invoice:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to retrieve invoice details'
        });
      }
    }),
  
  /**
   * List invoices for a tenant with filtering and pagination
   */
  listInvoices: authProcedure
    .input(z.object({
      tenantId: z.string(),
      limit: z.number().min(1).max(100).default(50),
      cursor: z.string().optional(),
      status: z.array(z.enum([
        InvoiceStatus.CAPTURED,
        InvoiceStatus.PROCESSING,
        InvoiceStatus.EXTRACTED,
        InvoiceStatus.REVIEW_NEEDED,
        InvoiceStatus.APPROVED,
        InvoiceStatus.REJECTED,
        InvoiceStatus.EXPORTED,
        InvoiceStatus.FAILED
      ])).optional(),
      vendorId: z.string().optional(),
      createdById: z.string().optional(),
      dateRange: z.object({
        startDate: z.string(),
        endDate: z.string()
      }).optional(),
      minAmount: z.number().optional(),
      maxAmount: z.number().optional()
    }))
    .query(async ({ ctx, input }) => {
      try {
        // Validate tenant access
        if (ctx.user.tenantId !== input.tenantId && ctx.user.role !== 'system_admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to view invoices in this organization'
          });
        }
        
        // Build the query
        let query = firebase.firestore
          .collection('invoices')
          .where('tenantId', '==', input.tenantId)
          .limit(input.limit);
        
        // Apply filters
        if (input.status && input.status.length > 0) {
          // Firestore can't do 'where in' and inequality filters together
          // So we handle one status filter as a simple where clause
          query = query.where('status', '==', input.status[0]);
        }
        
        if (input.vendorId) {
          query = query.where('vendorId', '==', input.vendorId);
        }
        
        if (input.createdById) {
          query = query.where('createdById', '==', input.createdById);
        }
        
        // Apply cursor pagination if provided
        if (input.cursor) {
          const cursorDoc = await firebase.firestore
            .collection('invoices')
            .doc(input.cursor)
            .get();
            
          if (cursorDoc.exists) {
            query = query.startAfter(cursorDoc);
          }
        }
        
        // Execute the query
        const snapshot = await query.get();
        
        // Process results
        const invoices = [];
        let lastCursor = null;
        
        snapshot.forEach(doc => {
          const data = doc.data();
          
          // Apply in-memory filters that can't be done in Firestore
          
          // If multiple statuses were requested and this isn't the first one
          if (input.status && input.status.length > 1 && !input.status.includes(data.status)) {
            return;
          }
          
          // Date range filter
          if (input.dateRange) {
            const startDate = new Date(input.dateRange.startDate);
            const endDate = new Date(input.dateRange.endDate);
            const invoiceDate = data.issueDate instanceof Date 
              ? data.issueDate 
              : data.issueDate.toDate(); // Handle Firestore Timestamp
            
            if (invoiceDate < startDate || invoiceDate > endDate) {
              return;
            }
          }
          
          // Amount range filter
          if (input.minAmount && data.amount.total < input.minAmount) {
            return;
          }
          
          if (input.maxAmount && data.amount.total > input.maxAmount) {
            return;
          }
          
          // Add the invoice to results
          invoices.push({
            id: doc.id,
            ...data
          });
          
          lastCursor = doc.id;
        });
        
        return {
          invoices,
          nextCursor: invoices.length === input.limit ? lastCursor : null,
          total: invoices.length
        };
      } catch (error) {
        logger.error('Error listing invoices:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to retrieve invoices'
        });
      }
    }),
  
  /**
   * Create a new invoice upload record
   * This initializes the invoice in the system and prepares for processing
   */
  createInvoice: authProcedure
    .input(z.object({
      tenantId: z.string(),
      vendorId: z.string().optional(),
      invoiceNumber: z.string().optional(),
      issueDate: z.string().optional(),
      dueDate: z.string().optional(),
      amount: z.object({
        subtotal: z.number().optional(),
        tax: z.number().optional(),
        total: z.number().optional(),
        currency: z.string().optional()
      }).optional(),
      referenceNumber: z.string().optional(),
      description: z.string().optional(),
      notes: z.string().optional()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Validate tenant access
        if (ctx.user.tenantId !== input.tenantId && ctx.user.role !== 'system_admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You can only create invoices in your own organization'
          });
        }
        
        // Get tenant to check limits
        const tenantDoc = await firebase.firestore
          .collection('tenants')
          .doc(input.tenantId)
          .get();
        
        if (!tenantDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Organization not found'
          });
        }
        
        const tenantData = tenantDoc.data();
        
        // Check tenant status
        if (tenantData?.status !== 'active' && tenantData?.status !== 'trial') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Organization is not active'
          });
        }
        
        // Check invoice limit
        if (tenantData?.plan?.maxInvoicesPerMonth) {
          // Get the current month's invoice count
          const firstDayOfMonth = new Date();
          firstDayOfMonth.setDate(1);
          firstDayOfMonth.setHours(0, 0, 0, 0);
          
          const invoiceCount = await firebase.firestore
            .collection('invoices')
            .where('tenantId', '==', input.tenantId)
            .where('createdAt', '>=', firstDayOfMonth)
            .count()
            .get();
          
          if (invoiceCount.data().count >= tenantData.plan.maxInvoicesPerMonth) {
            throw new TRPCError({
              code: 'FORBIDDEN',
              message: 'Monthly invoice limit reached'
            });
          }
        }
        
        // Create the invoice in Firestore
        const invoiceRef = firebase.firestore.collection('invoices').doc();
        
        const now = new Date();
        
        const invoiceData = {
          tenantId: input.tenantId,
          createdById: ctx.user.uid,
          status: InvoiceStatus.CAPTURED,
          invoiceNumber: input.invoiceNumber || '',
          issueDate: input.issueDate ? new Date(input.issueDate) : now,
          dueDate: input.dueDate ? new Date(input.dueDate) : null,
          amount: {
            subtotal: input.amount?.subtotal || 0,
            tax: input.amount?.tax || 0,
            total: input.amount?.total || 0,
            currency: input.amount?.currency || 'USD'
          },
          vatLines: [],
          referenceNumber: input.referenceNumber || '',
          description: input.description || '',
          lineItems: [],
          attachments: [],
          processingDetails: {
            ocrTier: OcrTier.TIER_1,
            confidenceScore: 0,
            processingTimeMs: 0,
            reviewRequired: false
          },
          notes: input.notes || '',
          createdAt: now,
          updatedAt: now,
          vendorId: input.vendorId || null
        };
        
        await invoiceRef.set(invoiceData);
        
        logger.info(`Created new invoice: ${invoiceRef.id}`);
        
        return {
          id: invoiceRef.id,
          ...invoiceData
        };
      } catch (error) {
        logger.error('Error creating invoice:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create invoice'
        });
      }
    }),
  
  /**
   * Process an invoice with OCR
   * This sends the invoice to the processing pipeline
   */
  processInvoice: authProcedure
    .input(z.object({
      invoiceId: z.string(),
      forceTier: z.nativeEnum(OcrTier).optional()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the invoice
        const invoiceDoc = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .get();
        
        if (!invoiceDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invoice not found'
          });
        }
        
        const invoiceData = invoiceDoc.data();
        
        // Validate tenant access
        if (invoiceData?.tenantId !== ctx.user.tenantId && ctx.user.role !== 'system_admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to process this invoice'
          });
        }
        
        // Check if the invoice has attachments
        if (!invoiceData?.attachments || invoiceData.attachments.length === 0) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invoice has no attachments to process'
          });
        }
        
        // Update invoice status to processing
        await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .update({
            status: InvoiceStatus.PROCESSING,
            updatedAt: new Date(),
            'processingDetails.ocrTier': input.forceTier || OcrTier.TIER_1
          });
        
        // Get the tenant's AI tier access level
        const tenantDoc = await firebase.firestore
          .collection('tenants')
          .doc(invoiceData.tenantId)
          .get();
        
        const tenantData = tenantDoc.data();
        const aiTierAccess = tenantData?.plan?.features?.aiTiers || 1;
        
        // Prepare the message for the processing queue
        const message = {
          invoiceId: input.invoiceId,
          tenantId: invoiceData.tenantId,
          attachmentIds: invoiceData.attachments.map(a => a.id),
          forceTier: input.forceTier,
          aiTierAccess,
          userId: ctx.user.uid
        };
        
        // Check if PubSub topic is configured
        if (!env.PUBSUB_INVOICE_PROCESS_TOPIC) {
          logger.error('PubSub topic for invoice processing not configured');
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'OCR processing system not properly configured'
          });
        }
        
        // Send to Pub/Sub for processing
        const topic = pubsub.topic(env.PUBSUB_INVOICE_PROCESS_TOPIC);
        const messageId = await topic.publish(Buffer.from(JSON.stringify(message)));
        
        logger.info(`Invoice ${input.invoiceId} sent for processing, message ID: ${messageId}`);
        
        return {
          id: input.invoiceId,
          status: InvoiceStatus.PROCESSING,
          message: 'Invoice sent for processing'
        };
      } catch (error) {
        logger.error('Error processing invoice:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to process invoice'
        });
      }
    }),
  
  /**
   * Upload an attachment to an invoice
   * This generates a signed URL for direct upload to Cloud Storage
   */
  getUploadUrl: authProcedure
    .input(z.object({
      invoiceId: z.string(),
      fileName: z.string(),
      contentType: z.string(),
      sizeBytes: z.number()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the invoice
        const invoiceDoc = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .get();
        
        if (!invoiceDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invoice not found'
          });
        }
        
        const invoiceData = invoiceDoc.data();
        
        // Validate tenant access
        if (invoiceData?.tenantId !== ctx.user.tenantId && ctx.user.role !== 'system_admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update this invoice'
          });
        }
        
        // Generate a unique filename to avoid collisions
        const fileExtension = input.fileName.split('.').pop() || '';
        const uniqueFilename = `${input.invoiceId}-${Date.now()}.${fileExtension}`;
        const filePath = `invoices/${invoiceData.tenantId}/${input.invoiceId}/${uniqueFilename}`;
        
        // Generate a signed URL for upload
        const [url] = await firebase.storage.bucket()
          .file(filePath)
          .getSignedUrl({
            version: 'v4',
            action: 'write',
            expires: Date.now() + 15 * 60 * 1000, // 15 minutes
            contentType: input.contentType,
          });
        
        // Create an attachment record in the invoice
        const attachmentId = uniqueFilename;
        const attachment = {
          id: attachmentId,
          filename: input.fileName,
          contentType: input.contentType,
          size: input.sizeBytes,
          url: `gs://${firebase.storage.bucket().name}/${filePath}`,
          thumbnailUrl: null,
          uploadedAt: new Date(),
          processingStatus: 'pending'
        };
        
        // Update the invoice with the new attachment
        await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .update({
            attachments: firebase.firestore.FieldValue.arrayUnion(attachment),
            updatedAt: new Date()
          });
        
        logger.info(`Generated upload URL for invoice ${input.invoiceId}, file: ${filePath}`);
        
        return {
          uploadUrl: url,
          attachmentId,
          filePath
        };
      } catch (error) {
        logger.error('Error generating upload URL:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to generate upload URL'
        });
      }
    }),
  
  /**
   * Update an invoice's data
   * Users can update basic invoice details
   * Only approvers can update the approval status
   */
  updateInvoice: authProcedure
    .input(z.object({
      invoiceId: z.string(),
      invoiceNumber: z.string().optional(),
      issueDate: z.string().optional(),
      dueDate: z.string().optional(),
      amount: z.object({
        subtotal: z.number().optional(),
        tax: z.number().optional(),
        total: z.number().optional(),
        currency: z.string().optional()
      }).optional(),
      referenceNumber: z.string().optional(),
      description: z.string().optional(),
      notes: z.string().optional(),
      vendorId: z.string().optional()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the invoice
        const invoiceDoc = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .get();
        
        if (!invoiceDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invoice not found'
          });
        }
        
        const invoiceData = invoiceDoc.data();
        
        // Validate tenant access
        if (invoiceData?.tenantId !== ctx.user.tenantId && ctx.user.role !== 'system_admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update this invoice'
          });
        }
        
        // Build update object
        const updates: Record<string, any> = {
          updatedAt: new Date()
        };
        
        if (input.invoiceNumber !== undefined) {
          updates.invoiceNumber = input.invoiceNumber;
        }
        
        if (input.issueDate) {
          updates.issueDate = new Date(input.issueDate);
        }
        
        if (input.dueDate) {
          updates.dueDate = new Date(input.dueDate);
        }
        
        if (input.amount) {
          // Merge with existing amount data
          updates.amount = {
            ...invoiceData?.amount,
            ...input.amount
          };
        }
        
        if (input.referenceNumber !== undefined) {
          updates.referenceNumber = input.referenceNumber;
        }
        
        if (input.description !== undefined) {
          updates.description = input.description;
        }
        
        if (input.notes !== undefined) {
          updates.notes = input.notes;
        }
        
        if (input.vendorId !== undefined) {
          updates.vendorId = input.vendorId;
        }
        
        // Update the invoice
        await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .update(updates);
        
        logger.info(`Updated invoice ${input.invoiceId}`);
        
        return {
          id: input.invoiceId,
          updated: true
        };
      } catch (error) {
        logger.error('Error updating invoice:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update invoice'
        });
      }
    }),
  
  /**
   * Update an invoice's status
   * Only approvers or higher can change approval status
   */
  updateStatus: approverProcedure
    .input(z.object({
      invoiceId: z.string(),
      status: z.enum([
        InvoiceStatus.APPROVED,
        InvoiceStatus.REJECTED,
        InvoiceStatus.EXPORTED
      ]),
      notes: z.string().optional()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the invoice
        const invoiceDoc = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .get();
        
        if (!invoiceDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invoice not found'
          });
        }
        
        const invoiceData = invoiceDoc.data();
        
        // Validate tenant access
        if (invoiceData?.tenantId !== ctx.user.tenantId && ctx.user.role !== 'system_admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update this invoice'
          });
        }
        
        // Update the status
        const updates: Record<string, any> = {
          status: input.status,
          updatedAt: new Date()
        };
        
        // Add notes if provided
        if (input.notes) {
          updates.notes = input.notes;
        }
        
        // Track who approved/rejected the invoice
        updates['processingDetails.reviewedBy'] = ctx.user.uid;
        updates['processingDetails.reviewedAt'] = new Date();
        
        // Update the invoice
        await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .update(updates);
        
        logger.info(`Updated invoice ${input.invoiceId} status to ${input.status}`);
        
        return {
          id: input.invoiceId,
          status: input.status,
          updated: true
        };
      } catch (error) {
        logger.error('Error updating invoice status:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update invoice status'
        });
      }
    }),
  
  /**
   * Delete an invoice
   * Soft deletes by marking the invoice as deleted
   */
  deleteInvoice: authProcedure
    .input(z.object({
      invoiceId: z.string()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the invoice
        const invoiceDoc = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .get();
        
        if (!invoiceDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invoice not found'
          });
        }
        
        const invoiceData = invoiceDoc.data();
        
        // Validate tenant access
        if (invoiceData?.tenantId !== ctx.user.tenantId && ctx.user.role !== 'system_admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to delete this invoice'
          });
        }
        
        // Check if the user has permission to delete invoices
        // Regular users can only delete their own invoices
        if (
          invoiceData?.createdById !== ctx.user.uid && 
          !['tenant_admin', 'system_admin'].includes(ctx.user.role)
        ) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You can only delete invoices you created'
          });
        }
        
        // Soft delete by marking the invoice as deleted
        await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .update({
            deleted: true,
            deletedAt: new Date(),
            deletedBy: ctx.user.uid,
            updatedAt: new Date()
          });
        
        logger.info(`Soft deleted invoice ${input.invoiceId}`);
        
        return {
          id: input.invoiceId,
          deleted: true
        };
      } catch (error) {
        logger.error('Error deleting invoice:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete invoice'
        });
      }
    })
});
