/**
 * Accounting API Router
 * 
 * Provides tRPC endpoints for managing accounting provider connections,
 * retrieving status, and triggering data synchronization.
 */

import { z } from 'zod';
import { router, publicProcedure, protectedProcedure } from '../trpc';
import { TRPCError } from '@trpc/server';
import { getAccountingProvider } from '../services/accounting/factory';
import { tokenStore } from '../services/accounting/tokenStore';
import { syncTenant } from '../services/accounting/sync/nightlySync';
import { tenantHasPermission } from '../auth/permissions';
import { getFirebase } from '../config/firebase';

// Schemas for input validation
const providerParamsSchema = z.object({
  tenantId: z.string(),
  providerType: z.enum(['qbo', 'xero', 'null']),
});

const connectParamsSchema = providerParamsSchema.extend({
  redirectUri: z.string().url(),
});

const completeOAuthSchema = connectParamsSchema.extend({
  code: z.string(),
  realmId: z.string().optional(),
});

const pushTransactionSchema = z.object({
  tenantId: z.string(),
  transactionId: z.string(),
  transactionType: z.enum(['BILL', 'SALES']),
  providerType: z.enum(['qbo', 'xero', 'null']).optional(),
  force: z.boolean().optional(),
});

const limitsSchema = z.object({
  tenantId: z.string(),
  limits: z.object({
    maxBillAmt: z.number().nullable(),
    maxInvoiceAmt: z.number().nullable(),
  }),
});

export const accountingRouter = router({
  /**
   * Get provider connection status and details
   */
  getProviderStatus: protectedProcedure
    .input(providerParamsSchema)
    .query(async ({ input, ctx }) => {
      try {
        // Check user has permission to access this tenant
        if (!tenantHasPermission(ctx.user, input.tenantId)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to access this tenant',
          });
        }
        
        // Get token information from the store
        const tokenInfo = await tokenStore.getTokens(input.tenantId, input.providerType);
        
        if (!tokenInfo || tokenInfo.status !== 'OK') {
          return {
            status: tokenInfo?.status || 'DISCONNECTED',
          };
        }
        
        // Get provider configuration and limits from database
        const providerConfig = await tokenStore.getProviderConfig(input.tenantId, input.providerType);
        
        return {
          status: tokenInfo.status,
          realmId: tokenInfo.realmId,
          lastSync: tokenInfo.lastSyncTime || null,
          tokenExpiry: tokenInfo.expiresAt || null,
          limits: {
            maxBillAmt: providerConfig?.limits?.maxBillAmt || null,
            maxInvoiceAmt: providerConfig?.limits?.maxInvoiceAmt || null,
          },
        };
      } catch (error) {
        console.error('Error getting provider status:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to get provider status: ${(error as Error).message}`,
        });
      }
    }),

  /**
   * Generate OAuth connection URL
   */
  generateConnectUrl: protectedProcedure
    .input(connectParamsSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Check user has permission to access this tenant
        if (!tenantHasPermission(ctx.user, input.tenantId)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to access this tenant',
          });
        }
        
        // Get provider instance for this tenant
        const provider = getAccountingProvider(input.tenantId, input.providerType);
        
        // Generate OAuth URL
        const url = await provider.generateAuthUrl(input.redirectUri);
        
        return { url };
      } catch (error) {
        console.error('Error generating connect URL:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to generate connect URL: ${(error as Error).message}`,
        });
      }
    }),
    
  /**
   * Complete OAuth flow with authorization code
   */
  completeOAuth: protectedProcedure
    .input(completeOAuthSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Check user has permission to access this tenant
        if (!tenantHasPermission(ctx.user, input.tenantId)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to access this tenant',
          });
        }
        
        // Get provider instance for this tenant
        const provider = getAccountingProvider(input.tenantId, input.providerType);
        
        // Complete OAuth flow and connect the provider
        await provider.connect(input.code, input.redirectUri, input.realmId);
        
        // Trigger an initial sync to populate reference data
        syncTenant(input.tenantId, input.providerType)
          .catch(error => {
            console.error(`Initial sync failed for tenant ${input.tenantId}:`, error);
          });
        
        return { success: true };
      } catch (error) {
        console.error('Error completing OAuth flow:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to complete OAuth connection: ${(error as Error).message}`,
        });
      }
    }),

  /**
   * Disconnect from provider
   */
  disconnectProvider: protectedProcedure
    .input(providerParamsSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Check user has admin permission to update this tenant
        if (!tenantHasPermission(ctx.user, input.tenantId, ['admin'])) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You need admin permission to disconnect accounting providers',
          });
        }
        
        // Delete tokens from store
        await tokenStore.deleteTokens(input.tenantId, input.providerType);
        
        return { success: true };
      } catch (error) {
        console.error('Error disconnecting provider:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to disconnect provider: ${(error as Error).message}`,
        });
      }
    }),

  /**
   * Update transaction limits
   */
  updateLimits: protectedProcedure
    .input(limitsSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Check user has admin permission to update this tenant
        if (!tenantHasPermission(ctx.user, input.tenantId, ['admin'])) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You need admin permission to update transaction limits',
          });
        }
        
        // Update provider configuration with new limits
        await tokenStore.updateProviderConfig(input.tenantId, 'qbo', {
          limits: input.limits,
        });
        
        return { success: true };
      } catch (error) {
        console.error('Error updating transaction limits:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to update transaction limits: ${(error as Error).message}`,
        });
      }
    }),

  /**
   * Trigger manual sync for a tenant
   */
  triggerManualSync: protectedProcedure
    .input(providerParamsSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Check user has permission to access this tenant
        if (!tenantHasPermission(ctx.user, input.tenantId)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to access this tenant',
          });
        }
        
        // Check if tenant is connected to the provider
        const tokenInfo = await tokenStore.getTokens(input.tenantId, input.providerType);
        
        if (!tokenInfo || tokenInfo.status !== 'OK') {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Tenant is not connected to this provider',
          });
        }
        
        // Trigger sync in the background
        syncTenant(input.tenantId, input.providerType)
          .catch(error => {
            console.error(`Background sync failed for tenant ${input.tenantId}:`, error);
          });
        
        return { success: true, message: 'Sync started' };
      } catch (error) {
        console.error('Error triggering manual sync:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to trigger manual sync: ${(error as Error).message}`,
        });
      }
    }),
    
  /**
   * Push a single transaction to the accounting system
   */
  pushTransaction: protectedProcedure
    .input(pushTransactionSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Check user has permission to access this tenant
        if (!tenantHasPermission(ctx.user, input.tenantId)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to access this tenant',
          });
        }
        
        const { tenantId, transactionId, transactionType, providerType = 'qbo', force = false } = input;

        // Get Firebase instance
        const firebase = await getFirebase();

        // Get the transaction from Firestore
        const transactionRef = firebase.firestore
          .collection('invoices')
          .doc(tenantId)
          .collection('items')
          .doc(transactionId);
        
        const transactionDoc = await transactionRef.get();
        
        if (!transactionDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Transaction not found',
          });
        }
        
        const transaction = transactionDoc.data();
        
        // Check if already pushed and not forced
        if (transaction?.status === 'PUSHED' && !force) {
          return {
            success: true,
            alreadyPushed: true,
            providerId: transaction?.providerId || transaction?.providerIds?.[providerType],
            message: 'Transaction was already pushed to the accounting system'
          };
        }
        
        // Get provider limits
        const config = await tokenStore.getProviderConfig(tenantId, providerType);
        const amount = transaction?.totalAmount || 0;
        
        // Check against limits if applicable
        if (!force) {
          if (transactionType === 'BILL' && config?.limits?.maxBillAmt !== null && amount > (config?.limits?.maxBillAmt || 0)) {
            // Update status to LIMIT_BLOCKED
            await transactionRef.update({
              status: 'LIMIT_BLOCKED',
              statusMessage: `Amount exceeds maximum bill amount limit of ${config?.limits?.maxBillAmt}`,
              updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
              updatedBy: ctx.user.uid
            });
            
            return {
              success: false,
              limitBlocked: true,
              message: `Bill amount ${amount} exceeds limit of ${config?.limits?.maxBillAmt}`
            };
          }
          
          if (transactionType === 'SALES' && config?.limits?.maxInvoiceAmt !== null && amount > (config?.limits?.maxInvoiceAmt || 0)) {
            // Update status to LIMIT_BLOCKED
            await transactionRef.update({
              status: 'LIMIT_BLOCKED',
              statusMessage: `Amount exceeds maximum invoice amount limit of ${config?.limits?.maxInvoiceAmt}`,
              updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
              updatedBy: ctx.user.uid
            });
            
            return {
              success: false,
              limitBlocked: true,
              message: `Invoice amount ${amount} exceeds limit of ${config?.limits?.maxInvoiceAmt}`
            };
          }
        }
        
        // Get the accounting provider for this tenant
        const provider = getAccountingProvider(tenantId, providerType);
        
        // Update status to PUSHING
        await transactionRef.update({
          status: 'PUSHING',
          statusMessage: 'Pushing to accounting system...',
          updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
          updatedBy: ctx.user.uid
        });
        
        // Get attachment if there is one
        let fileBlob = undefined;
        
        if (transaction?.attachmentPath) {
          // Code to retrieve the file from storage would go here
          // This is a placeholder for the actual implementation
          // fileBlob = await getFileFromStorage(transaction.attachmentPath);
        }
        
        // Push to the appropriate provider based on type
        let result;
        if (transactionType === 'BILL') {
          result = await provider.pushBill(transaction, fileBlob);
        } else {
          result = await provider.pushSalesInvoice(transaction, fileBlob);
        }
        
        // Update transaction with result
        if (result.success) {
          await transactionRef.update({
            status: 'PUSHED',
            statusMessage: 'Successfully pushed to accounting system',
            providerId: result.providerId,
            providerIds: { [providerType]: result.providerId },
            lastPushedAt: firebase.firestore.FieldValue.serverTimestamp(),
            updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
            updatedBy: ctx.user.uid
          });
        } else {
          await transactionRef.update({
            status: 'ERROR',
            statusMessage: result.error?.message || 'Unknown error pushing to accounting system',
            errorCode: result.error?.code,
            errorRetryable: result.error?.retry || false,
            updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
            updatedBy: ctx.user.uid
          });
        }
        
        return {
          success: result.success,
          providerId: result.providerId,
          message: result.error?.message || 'Successfully pushed to accounting system'
        };
      } catch (error) {
        console.error('Error pushing transaction:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to push transaction: ${(error as Error).message}`,
        });
      }
    }),
});
