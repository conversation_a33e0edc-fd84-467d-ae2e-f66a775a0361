import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, systemAdminProcedure, tenantAdminProcedure, authProcedure } from '../trpc/trpc';
import { getFirebase } from '../config/firebase';
import { logger } from '../config/logger';
import { TenantStatus } from '@billsnapp/shared-types';

/**
 * Tenants router for managing organization data in the multi-tenant system
 */
export const tenantsRouter = router({
  /**
   * Get a single tenant by ID
   * Users can only view their own tenant
   * System admins can view any tenant
   */
  getTenant: authProcedure
    .input(z.object({
      tenantId: z.string()
    }))
    .query(async ({ ctx, input }) => {
      try {
        // Regular users can only view their own tenant
        if (ctx.user.tenantId !== input.tenantId && ctx.user.role !== 'system_admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to view this organization'
          });
        }

        // Get Firebase instance
        const firebase = await getFirebase();

        // Get the tenant document
        const tenantDoc = await firebase.firestore
          .collection('tenants')
          .doc(input.tenantId)
          .get();
        
        if (!tenantDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Organization not found'
          });
        }
        
        return {
          id: tenantDoc.id,
          ...tenantDoc.data()
        };
      } catch (error) {
        logger.error('Error getting tenant:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to retrieve organization details'
        });
      }
    }),
  
  /**
   * List all tenants (system admin only)
   */
  listTenants: systemAdminProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(50),
      cursor: z.string().optional(),
      status: z.enum([
        TenantStatus.ACTIVE,
        TenantStatus.SUSPENDED,
        TenantStatus.TRIAL,
        TenantStatus.EXPIRED
      ]).optional(),
    }))
    .query(async ({ input }) => {
      try {
        // Get Firebase instance
        const firebase = await getFirebase();

        // Build the query
        let query = firebase.firestore
          .collection('tenants')
          .limit(input.limit);
        
        // Apply optional filters
        if (input.status) {
          query = query.where('status', '==', input.status);
        }
        
        // Apply cursor pagination if provided
        if (input.cursor) {
          const cursorDoc = await firebase.firestore
            .collection('tenants')
            .doc(input.cursor)
            .get();
            
          if (cursorDoc.exists) {
            query = query.startAfter(cursorDoc);
          }
        }
        
        // Execute the query
        const snapshot = await query.get();
        
        // Process results
        const tenants = [];
        let lastCursor = null;
        
        snapshot.forEach(doc => {
          tenants.push({
            id: doc.id,
            ...doc.data()
          });
          lastCursor = doc.id;
        });
        
        return {
          tenants,
          nextCursor: tenants.length === input.limit ? lastCursor : null,
          total: tenants.length
        };
      } catch (error) {
        logger.error('Error listing tenants:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to retrieve organizations'
        });
      }
    }),
  
  /**
   * Create a new tenant (system admin only)
   */
  createTenant: systemAdminProcedure
    .input(z.object({
      name: z.string().min(2),
      plan: z.object({
        name: z.string(),
        maxUsers: z.number().min(1),
        maxInvoicesPerMonth: z.number().min(0),
        features: z.object({
          allowAccountingIntegration: z.boolean(),
          allowMultipleApprovers: z.boolean(),
          allowAdvancedReporting: z.boolean(),
          allowCustomRules: z.boolean(),
          aiTiers: z.number().min(1).max(3)
        }),
        expiresAt: z.date().optional()
      }),
      settings: z.object({
        defaultCurrency: z.string().default('USD'),
        dateFormat: z.string().default('MM/dd/yyyy'),
        approvalRequired: z.boolean().default(true),
        autoExport: z.boolean().default(false),
        retentionPeriodDays: z.number().default(365),
        integrations: z.object({
          quickbooks: z.object({
            enabled: z.boolean().default(false),
            connectionId: z.string().optional(),
            lastSyncAt: z.date().optional()
          }).optional(),
          xero: z.object({
            enabled: z.boolean().default(false),
            connectionId: z.string().optional(),
            lastSyncAt: z.date().optional()
          }).optional()
        }).optional()
      }).optional()
    }))
    .mutation(async ({ input }) => {
      try {
        // Get Firebase instance
        const firebase = await getFirebase();

        // Check if tenant with this name already exists
        const existingTenants = await firebase.firestore
          .collection('tenants')
          .where('name', '==', input.name)
          .get();
        
        if (!existingTenants.empty) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'An organization with this name already exists'
          });
        }
        
        // Create the tenant in Firestore
        const tenantRef = firebase.firestore.collection('tenants').doc();
        
        await tenantRef.set({
          name: input.name,
          status: TenantStatus.ACTIVE,
          createdAt: new Date(),
          updatedAt: new Date(),
          plan: input.plan,
          settings: input.settings || {
            defaultCurrency: 'USD',
            dateFormat: 'MM/dd/yyyy',
            approvalRequired: true,
            autoExport: false,
            retentionPeriodDays: 365,
            integrations: {}
          },
          metadata: {}
        });
        
        logger.info(`Created new tenant: ${tenantRef.id}`);
        
        return {
          id: tenantRef.id,
          name: input.name,
          status: TenantStatus.ACTIVE
        };
      } catch (error) {
        logger.error('Error creating tenant:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create organization'
        });
      }
    }),
  
  /**
   * Update a tenant
   * Tenant admins can update their own tenant settings
   * System admins can update any tenant
   */
  updateTenant: tenantAdminProcedure
    .input(z.object({
      tenantId: z.string(),
      name: z.string().min(2).optional(),
      status: z.enum([
        TenantStatus.ACTIVE,
        TenantStatus.SUSPENDED,
        TenantStatus.TRIAL,
        TenantStatus.EXPIRED
      ]).optional(),
      plan: z.object({
        name: z.string().optional(),
        maxUsers: z.number().min(1).optional(),
        maxInvoicesPerMonth: z.number().min(0).optional(),
        features: z.object({
          allowAccountingIntegration: z.boolean().optional(),
          allowMultipleApprovers: z.boolean().optional(),
          allowAdvancedReporting: z.boolean().optional(),
          allowCustomRules: z.boolean().optional(),
          aiTiers: z.number().min(1).max(3).optional()
        }).optional(),
        expiresAt: z.date().optional().nullable()
      }).optional()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Regular tenant admins can only update their own tenant
        // and can't change status or plan details
        if (ctx.user.tenantId !== input.tenantId && ctx.user.role !== 'system_admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update this organization'
          });
        }
        
        // Only system admins can change status or plan
        if ((input.status || input.plan) && ctx.user.role !== 'system_admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Only system administrators can change organization status or plan'
          });
        }

        // Get Firebase instance
        const firebase = await getFirebase();

        // Get the tenant document
        const tenantRef = firebase.firestore
          .collection('tenants')
          .doc(input.tenantId);
        
        const tenantDoc = await tenantRef.get();
        
        if (!tenantDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Organization not found'
          });
        }
        
        // Build the update object
        const updates: Record<string, any> = {
          updatedAt: new Date()
        };
        
        if (input.name) {
          updates.name = input.name;
        }
        
        if (input.status) {
          updates.status = input.status;
        }
        
        if (input.plan) {
          // Merge with existing plan data
          const currentPlan = tenantDoc.data()?.plan || {};
          updates.plan = {
            ...currentPlan,
            ...input.plan,
            // Handle nested features object
            features: {
              ...(currentPlan.features || {}),
              ...(input.plan.features || {})
            }
          };
        }
        
        // Update the tenant in Firestore
        await tenantRef.update(updates);
        
        logger.info(`Updated tenant: ${input.tenantId}`);
        
        return {
          id: input.tenantId,
          updated: true
        };
      } catch (error) {
        logger.error('Error updating tenant:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update organization'
        });
      }
    }),
  
  /**
   * Update tenant settings
   * Tenant admins can update their own tenant settings
   */
  updateSettings: tenantAdminProcedure
    .input(z.object({
      tenantId: z.string(),
      settings: z.object({
        defaultCurrency: z.string().optional(),
        dateFormat: z.string().optional(),
        approvalRequired: z.boolean().optional(),
        autoExport: z.boolean().optional(),
        retentionPeriodDays: z.number().optional(),
        integrations: z.object({
          quickbooks: z.object({
            enabled: z.boolean().optional(),
            connectionId: z.string().optional(),
            lastSyncAt: z.date().optional()
          }).optional(),
          xero: z.object({
            enabled: z.boolean().optional(),
            connectionId: z.string().optional(),
            lastSyncAt: z.date().optional()
          }).optional()
        }).optional()
      })
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Regular tenant admins can only update their own tenant settings
        if (ctx.user.tenantId !== input.tenantId && ctx.user.role !== 'system_admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update settings for this organization'
          });
        }

        // Get Firebase instance
        const firebase = await getFirebase();

        // Get the tenant document
        const tenantRef = firebase.firestore
          .collection('tenants')
          .doc(input.tenantId);
        
        const tenantDoc = await tenantRef.get();
        
        if (!tenantDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Organization not found'
          });
        }
        
        // Get current settings
        const currentSettings = tenantDoc.data()?.settings || {};
        
        // Merge settings with current settings
        const updatedSettings = {
          ...currentSettings,
          ...input.settings,
          // Handle nested integrations object
          integrations: {
            ...(currentSettings.integrations || {}),
            ...(input.settings.integrations || {}),
            // Handle nested quickbooks object
            quickbooks: {
              ...(currentSettings.integrations?.quickbooks || {}),
              ...(input.settings.integrations?.quickbooks || {})
            },
            // Handle nested xero object
            xero: {
              ...(currentSettings.integrations?.xero || {}),
              ...(input.settings.integrations?.xero || {})
            }
          }
        };
        
        // Update the tenant in Firestore
        await tenantRef.update({
          settings: updatedSettings,
          updatedAt: new Date()
        });
        
        logger.info(`Updated settings for tenant: ${input.tenantId}`);
        
        return {
          id: input.tenantId,
          updated: true,
          settings: updatedSettings
        };
      } catch (error) {
        logger.error('Error updating tenant settings:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update organization settings'
        });
      }
    })
});
