import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, tenantAdminProcedure, systemAdminProcedure, authProcedure } from '../trpc/trpc';
import { getFirebase } from '../config/firebase';
import { logger } from '../config/logger';
import { UserRole, UserStatus } from '@billsnapp/shared-types';
import { setCustomClaims, generateEmailAlias, mapBillsnappRoleToApi, mapApiToBillsnappRole } from '../utils/auth-claims';

/**
 * Users router for managing users within tenants
 */
export const usersRouter = router({
  /**
   * Get users within the caller's tenant
   * Tenant admins can see all users in their tenant
   * Regular users can only see their own profile
   */
  listUsers: authProcedure
    .input(z.object({
      tenantId: z.string(),
      limit: z.number().min(1).max(100).default(50),
      cursor: z.string().optional(),
      status: z.enum(['active', 'disabled', 'pending', 'invited']).optional(),
      role: z.enum(['invoice_capturer', 'approver', 'accountant', 'tenant_admin', 'system_admin', 'snapper', 'checker', 'approver']).optional(),
    }))
    .query(async ({ ctx, input }) => {
      try {
        // Regular users can only list users if they're a tenant admin or system admin
        if (ctx.user.tenantId !== input.tenantId && ctx.user.role !== UserRole.SYSTEM_ADMIN) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to view users in this tenant'
          });
        }
        
        // Only allow admins to list users
        if (ctx.user.role !== UserRole.TENANT_ADMIN && ctx.user.role !== UserRole.SYSTEM_ADMIN) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Only administrators can list users'
          });
        }

        // Get Firebase instance
        const firebase = await getFirebase();

        // Build the query
        let query = firebase.firestore
          .collection('users')
          .where('tenantId', '==', input.tenantId)
          .limit(input.limit);
        
        // Apply optional filters
        if (input.status) {
          query = query.where('status', '==', input.status);
        }
        
        if (input.role) {
          query = query.where('role', '==', input.role);
        }
        
        // Apply cursor pagination if provided
        if (input.cursor) {
          const cursorDoc = await firebase.firestore
            .collection('users')
            .doc(input.cursor)
            .get();
            
          if (cursorDoc.exists) {
            query = query.startAfter(cursorDoc);
          }
        }
        
        // Execute the query
        const snapshot = await query.get();
        
        // Process results
        const users: Array<Record<string, any>> = [];
        let lastCursor: string | null = null;
        
        snapshot.forEach((doc: FirebaseFirestore.QueryDocumentSnapshot<FirebaseFirestore.DocumentData>) => {
          users.push({
            id: doc.id,
            ...doc.data()
          });
          lastCursor = doc.id;
        });
        
        return {
          users,
          nextCursor: users.length === input.limit ? lastCursor : null,
          total: users.length
        };
      } catch (error) {
        logger.error('Error listing users:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to retrieve users'
        });
      }
    }),
  
  /**
   * Create a new user in the tenant
   * Only tenant admins can create users in their tenant
   * Only system admins can create tenant admins
   */
  createUser: tenantAdminProcedure
    .input(z.object({
      email: z.string().email(),
      displayName: z.string().min(2),
      role: z.enum([
        UserRole.INVOICE_CAPTURER,
        UserRole.APPROVER,
        UserRole.ACCOUNTANT,
        UserRole.TENANT_ADMIN,
        // Include Billsnapp-specific roles as strings
        'snapper' as any,
        'checker' as any,
        'approver' as any
      ]),
      tenantId: z.string(),
      photoURL: z.string().url().optional(),
      pushLimit: z.number().min(0).optional(), // For Checker role (amount limit)
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Tenant admins can only create users in their own tenant
        if (ctx.user.tenantId !== input.tenantId && ctx.user.role !== UserRole.SYSTEM_ADMIN) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You can only create users in your own organization'
          });
        }
        
        // Only system admins can create tenant admins
        if (input.role === UserRole.TENANT_ADMIN && ctx.user.role !== UserRole.SYSTEM_ADMIN) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Only system administrators can create tenant administrators'
          });
        }

        // Get Firebase instance
        const firebase = await getFirebase();

        // Check if user already exists in Firebase Auth
        try {
          const existingUser = await firebase.auth.getUserByEmail(input.email);
          
          if (existingUser) {
            throw new TRPCError({
              code: 'CONFLICT',
              message: 'A user with this email already exists'
            });
          }
        } catch (error: any) {
          // Error code auth/user-not-found means the user doesn't exist, which is what we want
          if (error.code !== 'auth/user-not-found') {
            throw error;
          }
        }
        
        // Create the user in Firebase Auth
        const userRecord = await firebase.auth.createUser({
          email: input.email,
          displayName: input.displayName,
          photoURL: input.photoURL,
          emailVerified: false,
        });
        
        // Create a custom invitation link for the user
        const actionCodeSettings = {
          url: `${process.env.FRONTEND_URL}/complete-signup?uid=${userRecord.uid}`,
          handleCodeInApp: true,
        };
        
        const inviteLink = await firebase.auth.generatePasswordResetLink(
          input.email,
          actionCodeSettings
        );
        
        // Map role if it's a Billsnapp-specific role string
        let apiRole = input.role as UserRole;
        if (typeof input.role === 'string' && ['snapper', 'checker', 'approver'].includes(input.role)) {
          apiRole = mapBillsnappRoleToApi(input.role);
        }
        
        // Generate email alias for the user
        const emailAlias = generateEmailAlias(input.tenantId, userRecord.uid);
        
        // Set custom claims for role-based access control
        await setCustomClaims(userRecord.uid, {
          role: apiRole,
          tenantId: input.tenantId,
          emailAlias,
          // Add push limit for Checker role if specified
          ...(apiRole === UserRole.APPROVER && input.pushLimit ? { pushLimit: input.pushLimit } : {})
        });
        
        // Create user document in Firestore
        await firebase.firestore
          .collection('users')
          .doc(userRecord.uid)
          .set({
            uid: userRecord.uid,
            email: input.email,
            displayName: input.displayName,
            photoURL: input.photoURL,
            role: apiRole,
            billsnappRole: typeof input.role === 'string' && ['snapper', 'checker', 'approver'].includes(input.role) 
              ? input.role 
              : mapApiToBillsnappRole(apiRole),
            tenantId: input.tenantId,
            emailAlias,
            pushLimit: apiRole === UserRole.APPROVER && input.pushLimit ? input.pushLimit : null,
            status: UserStatus.INVITED,
            createdAt: new Date(),
            updatedAt: new Date(),
            inviteLink,
            preferences: {
              theme: 'system',
              notifications: {
                email: true,
                push: true,
                inApp: true
              },
              defaultView: 'dashboard'
            }
          });
        
        logger.info(`Created new user ${userRecord.uid} in tenant ${input.tenantId}`);
        
        return {
          uid: userRecord.uid,
          email: input.email,
          displayName: input.displayName,
          inviteLink
        };
      } catch (error) {
        logger.error('Error creating user:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create user'
        });
      }
    }),
  
  /**
   * Update a user's profile
   * Users can update their own profiles
   * Tenant admins can update any user in their tenant
   * System admins can update any user
   */
  updateUser: authProcedure
    .input(z.object({
      uid: z.string(),
      displayName: z.string().min(2).optional(),
      photoURL: z.string().url().optional(),
      role: z.union([
        z.enum([
          UserRole.INVOICE_CAPTURER,
          UserRole.APPROVER,
          UserRole.ACCOUNTANT,
          UserRole.TENANT_ADMIN,
          UserRole.SYSTEM_ADMIN
        ]),
        z.enum(['snapper', 'checker', 'approver'])
      ]).optional(),
      pushLimit: z.number().min(0).optional(), // Amount limit for Checker role
      status: z.enum([
        UserStatus.ACTIVE,
        UserStatus.DISABLED,
        UserStatus.PENDING,
        UserStatus.INVITED
      ]).optional(),
      preferences: z.object({
        theme: z.enum(['light', 'dark', 'system']).optional(),
        notifications: z.object({
          email: z.boolean().optional(),
          push: z.boolean().optional(),
          inApp: z.boolean().optional()
        }).optional(),
        defaultView: z.string().optional()
      }).optional()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get Firebase instance
        const firebase = await getFirebase();

        // Get the user's current data to check permissions
        const userDoc = await firebase.firestore
          .collection('users')
          .doc(input.uid)
          .get();
        
        if (!userDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found'
          });
        }
        
        const userData = userDoc.data();
        
        // Check permissions
        const isOwnProfile = ctx.user.uid === input.uid;
        const isTenantAdmin = ctx.user.role === UserRole.TENANT_ADMIN && ctx.user.tenantId === userData?.tenantId;
        const isSystemAdmin = ctx.user.role === UserRole.SYSTEM_ADMIN;
        
        if (!isOwnProfile && !isTenantAdmin && !isSystemAdmin) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update this user'
          });
        }
        
        // Only admins can change roles or status
        if ((input.role || input.status) && !isTenantAdmin && !isSystemAdmin) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Only administrators can change roles or account status'
          });
        }
        
        // Update the user in Firebase Auth
        const authUpdates: Record<string, any> = {};
        
        if (input.displayName) {
          authUpdates.displayName = input.displayName;
        }
        
        if (input.photoURL) {
          authUpdates.photoURL = input.photoURL;
        }
        
        if (input.status === UserStatus.DISABLED) {
          authUpdates.disabled = true;
        } else if (input.status === UserStatus.ACTIVE) {
          authUpdates.disabled = false;
        }
        
        if (Object.keys(authUpdates).length > 0) {
          await firebase.auth.updateUser(input.uid, authUpdates);
        }
        
        // Update the user in Firestore
        const updates: Record<string, any> = {
          updatedAt: new Date()
        };
        
        if (input.displayName) {
          updates.displayName = input.displayName;
        }
        
        if (input.photoURL) {
          updates.photoURL = input.photoURL;
        }
        
        if (input.role) {
          // Map role if it's a Billsnapp-specific role string
          if (typeof input.role === 'string' && ['snapper', 'checker', 'approver'].includes(input.role)) {
            const apiRole = mapBillsnappRoleToApi(input.role);
            updates.role = apiRole;
            updates.billsnappRole = input.role;
            
            // Update Firebase Auth custom claims
            await setCustomClaims(input.uid, {
              role: apiRole
            });
          } else {
            updates.role = input.role;
            updates.billsnappRole = mapApiToBillsnappRole(input.role as UserRole);
            
            // Update Firebase Auth custom claims
            await setCustomClaims(input.uid, {
              role: input.role as UserRole
            });
          }
        }
        
        // Handle push limit for Checker role
        if (input.pushLimit !== undefined) {
          updates.pushLimit = input.pushLimit;
          
          // If role is Checker or was already Checker, update push limit in custom claims
          const isChecker = updates.role === UserRole.APPROVER || 
                            updates.billsnappRole === 'checker' ||
                            (userData?.role === UserRole.APPROVER && !updates.role);
          
          if (isChecker) {
            await setCustomClaims(input.uid, {
              pushLimit: input.pushLimit
            });
          }
        }
        
        if (input.status) {
          updates.status = input.status;
        }
        
        if (input.preferences) {
          // Merge preferences with existing ones
          updates.preferences = {
            ...userData?.preferences,
            ...input.preferences,
            // Merge nested notification settings
            notifications: {
              ...(userData?.preferences?.notifications || {}),
              ...(input.preferences.notifications || {})
            }
          };
        }
        
        await firebase.firestore
          .collection('users')
          .doc(input.uid)
          .update(updates);
        
        logger.info(`Updated user ${input.uid}`);
        
        return {
          uid: input.uid,
          updated: true
        };
      } catch (error) {
        logger.error('Error updating user:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update user'
        });
      }
    }),
  
  /**
   * Delete a user
   * Only tenant admins can delete users in their tenant
   * Only system admins can delete tenant admins
   */
  deleteUser: tenantAdminProcedure
    .input(z.object({
      uid: z.string()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get Firebase instance
        const firebase = await getFirebase();

        // Get the user's current data to check permissions
        const userDoc = await firebase.firestore
          .collection('users')
          .doc(input.uid)
          .get();
        
        if (!userDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found'
          });
        }
        
        const userData = userDoc.data();
        
        // Check permissions
        const isTenantAdmin = ctx.user.role === UserRole.TENANT_ADMIN && ctx.user.tenantId === userData?.tenantId;
        const isSystemAdmin = ctx.user.role === UserRole.SYSTEM_ADMIN;
        
        if (!isTenantAdmin && !isSystemAdmin) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to delete this user'
          });
        }
        
        // Only system admins can delete tenant admins
        if (userData?.role === UserRole.TENANT_ADMIN && !isSystemAdmin) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Only system administrators can delete tenant administrators'
          });
        }
        
        // Can't delete yourself
        if (ctx.user.uid === input.uid) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'You cannot delete your own account'
          });
        }
        
        // Delete the user from Firestore first (to maintain data integrity)
        await firebase.firestore
          .collection('users')
          .doc(input.uid)
          .delete();
        
        // Then delete from Firebase Auth
        await firebase.auth.deleteUser(input.uid);
        
        logger.info(`Deleted user ${input.uid}`);
        
        return {
          uid: input.uid,
          deleted: true
        };
      } catch (error) {
        logger.error('Error deleting user:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete user'
        });
      }
    })
});
