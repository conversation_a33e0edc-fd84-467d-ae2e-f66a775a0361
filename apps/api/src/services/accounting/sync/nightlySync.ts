/**
 * Nightly Sync Job for Accounting Providers
 * 
 * This Cloud Function runs on a schedule to synchronize reference data
 * (accounts, vendors, customers) from accounting systems for all connected tenants.
 */

import { getFirebase } from '../../../config/firebase';
import { getProvider, clearProviderCache } from '../factory';
import { listTenantsWithProvider } from '../tokenStore';
import { ProviderType } from '../types';

// Firestore collections for reference data
const COLLECTIONS = {
  ACCOUNTS: 'qbAccounts',
  VENDORS: 'vendors',
  CUSTOMERS: 'customers',
};

/**
 * Synchronize reference data for all tenants connected to a specific provider
 * 
 * @param providerType - The provider type to sync
 */
export async function syncAllTenants(providerType: ProviderType): Promise<void> {
  try {
    console.log(`Starting reference data sync for provider: ${providerType}`);
    
    // Get all tenants connected to the provider
    const tenantIds = await listTenantsWithProvider(providerType);
    
    console.log(`Found ${tenantIds.length} tenants connected to ${providerType}`);
    
    // Track success and failure counts
    let successCount = 0;
    let failureCount = 0;
    
    // Synchronize each tenant
    for (const tenantId of tenantIds) {
      try {
        await syncTenant(tenantId, providerType);
        successCount++;
      } catch (error) {
        console.error(`Failed to sync tenant ${tenantId}:`, error);
        failureCount++;
      }
    }
    
    console.log(`Completed reference data sync for provider: ${providerType}`);
    console.log(`Success: ${successCount}, Failure: ${failureCount}`);
  } catch (error) {
    console.error(`Error in syncAllTenants for provider ${providerType}:`, error);
    throw error;
  } finally {
    // Clear provider cache to free up resources
    clearProviderCache();
  }
}

/**
 * Synchronize reference data for a single tenant
 * 
 * @param tenantId - The tenant ID
 * @param providerType - The provider type
 */
export async function syncTenant(tenantId: string, providerType: ProviderType): Promise<void> {
  console.log(`Syncing tenant ${tenantId} for provider ${providerType}`);
  
  // Get provider instance
  const provider = getProvider(tenantId, providerType);
  
  try {
    // Sync accounts
    const accounts = await provider.getAccounts();
    await syncCollection(COLLECTIONS.ACCOUNTS, tenantId, accounts);
    console.log(`Synced ${accounts.length} accounts for tenant ${tenantId}`);
    
    // Sync vendors
    const vendors = await provider.getVendors();
    await syncCollection(COLLECTIONS.VENDORS, tenantId, vendors);
    console.log(`Synced ${vendors.length} vendors for tenant ${tenantId}`);
    
    // Sync customers
    const customers = await provider.getCustomers();
    await syncCollection(COLLECTIONS.CUSTOMERS, tenantId, customers);
    console.log(`Synced ${customers.length} customers for tenant ${tenantId}`);
    
    // Update last sync timestamp
    await updateLastSyncTimestamp(tenantId, providerType);
    
    console.log(`Successfully synced all reference data for tenant ${tenantId}`);
  } catch (error) {
    console.error(`Error syncing tenant ${tenantId}:`, error);
    throw error;
  }
}

/**
 * Synchronize a collection with data from provider
 * 
 * @param collectionName - Firestore collection name
 * @param tenantId - Tenant ID
 * @param items - Items to sync
 */
async function syncCollection(collectionName: string, tenantId: string, items: any[]): Promise<void> {
  // Get Firebase instance
  const firebase = await getFirebase();
  const db = firebase.firestore;

  // Get a reference to the tenant's collection
  const collectionRef = db.collection(collectionName).doc(tenantId).collection('items');
  
  // Get existing items
  const snapshot = await collectionRef.get();
  const existingItems = new Map<string, any>();
  
  snapshot.forEach(doc => {
    existingItems.set(doc.id, doc.data());
  });
  
  // Prepare batch operations
  const batchSize = 500; // Firestore batch limit is 500
  let batch = db.batch();
  let operationCount = 0;
  
  // Track which items still exist in the provider
  const stillExistingIds = new Set<string>();
  
  // Upsert items from provider
  for (const item of items) {
    if (!item.id) {
      console.warn('Item has no ID, skipping:', item);
      continue;
    }
    
    stillExistingIds.add(item.id);
    const docRef = collectionRef.doc(item.id);
    
    batch.set(docRef, {
      ...item,
      _updatedAt: new Date().toISOString(),
    }, { merge: true });
    
    operationCount++;
    
    // If we reach the batch limit, commit and create a new batch
    if (operationCount >= batchSize) {
      await batch.commit();
      batch = db.batch();
      operationCount = 0;
    }
  }
  
  // Mark items that no longer exist as inactive
  for (const [id, item] of existingItems.entries()) {
    if (!stillExistingIds.has(id) && item.active !== false) {
      const docRef = collectionRef.doc(id);
      
      batch.update(docRef, {
        active: false,
        _updatedAt: new Date().toISOString(),
      });
      
      operationCount++;
      
      // If we reach the batch limit, commit and create a new batch
      if (operationCount >= batchSize) {
        await batch.commit();
        batch = db.batch();
        operationCount = 0;
      }
    }
  }
  
  // Commit any remaining operations
  if (operationCount > 0) {
    await batch.commit();
  }
}

/**
 * Update the last sync timestamp for a tenant
 * 
 * @param tenantId - Tenant ID
 * @param providerType - Provider type
 */
async function updateLastSyncTimestamp(tenantId: string, providerType: ProviderType): Promise<void> {
  // Get Firebase instance
  const firebase = await getFirebase();
  const db = firebase.firestore;

  await db.collection('tenants').doc(tenantId).update({
    [`providers.${providerType}.lastSync`]: new Date().toISOString(),
  });
}

/**
 * Handler for the scheduled nightly sync job
 * 
 * This function is triggered by Cloud Scheduler.
 */
export async function handleNightlySync(): Promise<void> {
  try {
    // For Phase 1, we only sync QBO
    await syncAllTenants('qbo');
    
    // In the future, we can add more providers here
    // await syncAllTenants('xero');
  } catch (error) {
    console.error('Error in handleNightlySync:', error);
    throw error;
  }
}
