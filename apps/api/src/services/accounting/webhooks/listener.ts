/**
 * Accounting System Webhook Listener
 * 
 * This module handles incoming webhook events from accounting systems,
 * validates their authenticity, and routes them to the appropriate handlers.
 * 
 * QuickBooks Online uses a webhook notification system that sends POST requests
 * when entities like accounts, customers, or invoices are created, updated, or deleted.
 * 
 * @module accounting/webhooks/listener
 */

import crypto from 'crypto';
import { Request, Response } from 'express';
import { logger } from '../../../config/logger';
import { getFirebase } from '../../../config/firebase';
import { ProviderType } from '../types';
import { getEventHandler } from './handlers';
import { IncomingWebhookEvent, WebhookEventType } from './types';

// Firestore collection for tracking processed webhook events
const EVENTS_COLLECTION = 'accountingWebhookEvents';

/**
 * Verify the authenticity of a QuickBooks webhook request
 * 
 * @param req - Express request
 * @param verifier - Verifier token from Intuit Developer account
 * @returns Whether the signature is valid
 */
export function verifyQboWebhook(req: Request, verifier: string): boolean {
  try {
    // Get the signature from the request headers
    const signature = req.headers['intuit-signature'] as string;
    if (!signature) {
      logger.warn('Missing Intuit signature header');
      return false;
    }

    // Get the raw body as a string
    const rawBody = JSON.stringify(req.body);

    // Create an HMAC-SHA256 hash using the verifier token
    const hmac = crypto.createHmac('sha256', verifier);
    hmac.update(rawBody);
    const computedSignature = hmac.digest('base64');

    // Compare the signatures
    return signature === computedSignature;
  } catch (error) {
    logger.error('Error verifying QBO webhook', { error });
    return false;
  }
}

/**
 * Check if a webhook event has already been processed
 * 
 * @param eventId - Unique event ID from the provider
 * @returns Whether the event has been processed
 */
async function isEventProcessed(eventId: string): Promise<boolean> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();

    const eventRef = firebase.firestore.collection(EVENTS_COLLECTION).doc(eventId);
    const doc = await eventRef.get();
    return doc.exists;
  } catch (error) {
    logger.error('Error checking event status', { error });
    return false; // Assume not processed in case of error
  }
}

/**
 * Mark a webhook event as processed
 * 
 * @param event - The webhook event
 * @returns Promise that resolves when the event is marked as processed
 */
async function markEventProcessed(event: IncomingWebhookEvent): Promise<void> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();

    const eventRef = firebase.firestore.collection(EVENTS_COLLECTION).doc(event.eventId);
    await eventRef.set({
      providerType: event.providerType,
      tenantId: event.tenantId,
      eventType: event.eventType,
      entityId: event.entityId,
      entityType: event.entityType,
      timestamp: firebase.firestore.FieldValue.serverTimestamp(),
      processed: true
    });
  } catch (error) {
    logger.error('Error marking event as processed', { error, event });
  }
}

/**
 * Process an incoming webhook event
 * 
 * @param event - The webhook event
 * @returns Promise that resolves when the event is processed
 */
export async function processWebhookEvent(event: IncomingWebhookEvent): Promise<void> {
  try {
    // Check if event has already been processed
    if (await isEventProcessed(event.eventId)) {
      logger.info('Event already processed, skipping', { eventId: event.eventId });
      return;
    }

    // Get the appropriate handler for this event type
    const handler = getEventHandler(event.providerType, event.eventType);
    if (!handler) {
      logger.warn('No handler found for event', { 
        providerType: event.providerType, 
        eventType: event.eventType 
      });
      return;
    }

    // Process the event
    await handler(event);

    // Mark the event as processed
    await markEventProcessed(event);

    logger.info('Webhook event processed successfully', { 
      eventId: event.eventId,
      providerType: event.providerType,
      eventType: event.eventType,
      entityType: event.entityType
    });
  } catch (error) {
    logger.error('Error processing webhook event', { error, event });
    throw error;
  }
}

/**
 * Transform QBO webhook payload into our standardized event format
 * 
 * @param payload - Raw QBO webhook payload
 * @param tenantId - Tenant ID
 * @returns Standardized event object
 */
export function transformQboPayload(payload: any, tenantId: string): IncomingWebhookEvent | null {
  try {
    // QBO webhook payload structure:
    // {
    //   eventNotifications: [{
    //     realmId: string,
    //     dataChangeEvent: {
    //       entities: [{
    //         id: string,
    //         name: string,
    //         operation: string,
    //         lastUpdated: string
    //       }]
    //     }
    //   }]
    // }

    if (!payload.eventNotifications?.[0]) {
      logger.warn('Invalid QBO webhook payload format');
      return null;
    }

    const notification = payload.eventNotifications[0];
    const entity = notification.dataChangeEvent?.entities?.[0];

    if (!entity) {
      logger.warn('No entity in QBO webhook payload');
      return null;
    }

    // Map QBO operation to our event type
    let eventType: WebhookEventType;
    switch (entity.operation.toLowerCase()) {
      case 'create':
        eventType = WebhookEventType.CREATED;
        break;
      case 'update':
        eventType = WebhookEventType.UPDATED;
        break;
      case 'delete':
        eventType = WebhookEventType.DELETED;
        break;
      default:
        eventType = WebhookEventType.UPDATED;
    }

    return {
      eventId: `qbo_${notification.realmId}_${entity.name}_${entity.id}_${Date.now()}`,
      providerType: 'qbo' as ProviderType,
      tenantId,
      eventType,
      entityId: entity.id,
      entityType: entity.name.toLowerCase(),
      payload: entity,
      timestamp: new Date(entity.lastUpdated || Date.now()).toISOString()
    };
  } catch (error) {
    logger.error('Error transforming QBO payload', { error, payload });
    return null;
  }
}

/**
 * Main webhook handler for QuickBooks Online
 * This is designed to be used with Express or similar web frameworks
 * 
 * @param req - Express request
 * @param res - Express response
 * @param tenantId - Tenant ID
 * @param verifierToken - QBO webhook verifier token
 * @returns Promise that resolves when the webhook is handled
 */
export async function handleQboWebhook(
  req: Request, 
  res: Response, 
  tenantId: string,
  verifierToken: string
): Promise<void> {
  try {
    // Verify the webhook signature
    if (!verifyQboWebhook(req, verifierToken)) {
      logger.warn('Invalid QBO webhook signature', { tenantId });
      res.status(401).send('Invalid signature');
      return;
    }

    // Transform the payload
    const event = transformQboPayload(req.body, tenantId);
    if (!event) {
      res.status(400).send('Invalid payload format');
      return;
    }

    // Process the event (async)
    processWebhookEvent(event)
      .catch(error => {
        logger.error('Error in webhook event processing', { error, eventId: event.eventId });
      });

    // Return success immediately (don't wait for processing)
    res.status(200).send('Webhook received');
  } catch (error) {
    logger.error('Error handling QBO webhook', { error, tenantId });
    res.status(500).send('Internal server error');
  }
}
