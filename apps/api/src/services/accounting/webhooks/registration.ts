/**
 * Accounting Webhook Registration
 * 
 * This module provides functionality for registering and managing webhooks
 * with different accounting systems. It allows Billsnapp to receive real-time
 * notifications when entities are created, updated, or deleted in the
 * accounting system.
 * 
 * @module accounting/webhooks/registration
 */

import { logger } from '../../../config/logger';
import { getFirebase } from '../../../config/firebase';
import { getProvider } from '../factory';
import { ProviderType } from '../types';
import { WebhookRegistrationConfig, WebhookRegistrationResult } from './types';

// Firestore collection for tracking registered webhooks
const WEBHOOKS_COLLECTION = 'accountingWebhooks';

/**
 * Register a webhook with QuickBooks Online
 * 
 * @param tenantId - Tenant ID
 * @param config - Webhook registration configuration
 * @returns Result of the registration
 */
export async function registerQboWebhook(
  tenantId: string,
  config: WebhookRegistrationConfig
): Promise<WebhookRegistrationResult> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();

    // Get the QBO provider instance
    const provider = getProvider(tenantId, 'qbo');
    
    // Check if we already have an active webhook for this tenant
    const existingWebhook = await getExistingWebhook(tenantId, 'qbo');
    
    // If there's an existing webhook, update it
    if (existingWebhook) {
      // Update webhook in QBO
      const updatedWebhook = await provider.updateWebhook({
        webhookId: existingWebhook.webhookId,
        callbackUrl: config.callbackUrl,
        eventTypes: config.eventTypes,
        entityTypes: config.entityTypes
      });
      
      if (updatedWebhook.success) {
        // Update record in Firestore
        await firebase.firestore
          .collection(WEBHOOKS_COLLECTION)
          .doc(existingWebhook.docId)
          .update({
            callbackUrl: config.callbackUrl,
            verifierToken: config.verifierToken,
            eventTypes: config.eventTypes,
            entityTypes: config.entityTypes,
            updatedAt: firebase.firestore.FieldValue.serverTimestamp()
          });
        
        logger.info('Updated existing QBO webhook', { 
          tenantId, 
          webhookId: existingWebhook.webhookId 
        });
        
        return {
          success: true,
          webhookId: existingWebhook.webhookId
        };
      } else {
        return {
          success: false,
          error: updatedWebhook.error || 'Failed to update webhook in QBO'
        };
      }
    } else {
      // Create a new webhook in QBO
      const newWebhook = await provider.createWebhook({
        callbackUrl: config.callbackUrl,
        eventTypes: config.eventTypes,
        entityTypes: config.entityTypes
      });
      
      if (newWebhook.success && newWebhook.webhookId) {
        // Store webhook info in Firestore
        const webhookRef = firebase.firestore.collection(WEBHOOKS_COLLECTION).doc();
        await webhookRef.set({
          tenantId,
          providerType: 'qbo',
          webhookId: newWebhook.webhookId,
          callbackUrl: config.callbackUrl,
          verifierToken: config.verifierToken,
          eventTypes: config.eventTypes,
          entityTypes: config.entityTypes,
          createdAt: firebase.firestore.FieldValue.serverTimestamp(),
          updatedAt: firebase.firestore.FieldValue.serverTimestamp()
        });
        
        logger.info('Registered new QBO webhook', { 
          tenantId, 
          webhookId: newWebhook.webhookId 
        });
        
        return {
          success: true,
          webhookId: newWebhook.webhookId
        };
      } else {
        return {
          success: false,
          error: newWebhook.error || 'Failed to create webhook in QBO'
        };
      }
    }
  } catch (error) {
    logger.error('Error registering QBO webhook', { error, tenantId });
    return {
      success: false,
      error: (error as Error).message
    };
  }
}

/**
 * Get existing webhook information for a tenant and provider
 * 
 * @param tenantId - Tenant ID
 * @param providerType - Provider type
 * @returns Existing webhook info, or null if none exists
 */
async function getExistingWebhook(
  tenantId: string,
  providerType: ProviderType
): Promise<{ docId: string; webhookId: string } | null> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();

    const snapshot = await firebase.firestore
      .collection(WEBHOOKS_COLLECTION)
      .where('tenantId', '==', tenantId)
      .where('providerType', '==', providerType)
      .limit(1)
      .get();
    
    if (snapshot.empty) {
      return null;
    }
    
    const doc = snapshot.docs[0];
    return {
      docId: doc.id,
      webhookId: doc.data().webhookId
    };
  } catch (error) {
    logger.error('Error getting existing webhook', { error, tenantId, providerType });
    return null;
  }
}

/**
 * Delete a webhook registration
 * 
 * @param tenantId - Tenant ID
 * @param providerType - Provider type
 * @returns Whether the deletion was successful
 */
export async function deleteWebhookRegistration(
  tenantId: string,
  providerType: ProviderType
): Promise<boolean> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();

    // Get the existing webhook
    const existingWebhook = await getExistingWebhook(tenantId, providerType);
    
    if (!existingWebhook) {
      logger.info('No webhook found to delete', { tenantId, providerType });
      return true;
    }
    
    // Get the provider instance
    const provider = getProvider(tenantId, providerType);
    
    // Delete the webhook from the provider
    const result = await provider.deleteWebhook(existingWebhook.webhookId);
    
    if (result.success) {
      // Delete the record from Firestore
      await firebase.firestore
        .collection(WEBHOOKS_COLLECTION)
        .doc(existingWebhook.docId)
        .delete();
      
      logger.info('Deleted webhook registration', { 
        tenantId, 
        providerType, 
        webhookId: existingWebhook.webhookId 
      });
      
      return true;
    } else {
      logger.error('Error deleting webhook from provider', { 
        error: result.error,
        tenantId,
        providerType,
        webhookId: existingWebhook.webhookId
      });
      
      return false;
    }
  } catch (error) {
    logger.error('Error deleting webhook registration', { 
      error, 
      tenantId, 
      providerType 
    });
    
    return false;
  }
}

/**
 * Get the verifier token for a webhook
 * 
 * @param tenantId - Tenant ID
 * @param providerType - Provider type
 * @returns Verifier token, or null if not found
 */
export async function getWebhookVerifierToken(
  tenantId: string,
  providerType: ProviderType
): Promise<string | null> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();

    const snapshot = await firebase.firestore
      .collection(WEBHOOKS_COLLECTION)
      .where('tenantId', '==', tenantId)
      .where('providerType', '==', providerType)
      .limit(1)
      .get();
    
    if (snapshot.empty) {
      return null;
    }
    
    return snapshot.docs[0].data().verifierToken || null;
  } catch (error) {
    logger.error('Error getting webhook verifier token', { 
      error, 
      tenantId, 
      providerType 
    });
    
    return null;
  }
}
