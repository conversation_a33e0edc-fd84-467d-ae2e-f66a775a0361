/**
 * Push Worker Cloud Function
 * 
 * This worker handles the actual pushing of transactions to accounting systems.
 * It processes queued tasks from Cloud Tasks and provides retry capabilities.
 */

import * as functions from 'firebase-functions';
import { getFirebase } from '../../../config/firebase';
import { getProvider } from '../factory';
import { TransactionType } from '../types';
import { downloadAttachment, compressAttachmentIfNeeded } from './attachment';
import { MAX_ATTACHMENT_SIZE } from '../qbo/constants';

// Note: Firebase instance will be obtained lazily in functions

// Default region for Cloud Functions
const REGION = 'us-central1';

// Interface for push task payload
interface PushTaskPayload {
  tenantId: string;
  invoiceId: string;
  providerType: string;
  transactionType: TransactionType;
  idempotencyKey: string;
}

/**
 * Worker function to process push tasks
 */
export const pushWorkerFunction = functions
  .region(REGION)
  .runWith({
    timeoutSeconds: 300,
    memory: '1GB',
  })
  .https
  .onRequest(async (req, res) => {
    try {
      // Get Firebase instance
      const firebase = await getFirebase();
      const db = firebase.firestore;

      // Verify the request is from Cloud Tasks
      // In a real implementation, you would verify the Cloud Tasks JWT token

      // Parse the payload
      const payload = req.body as PushTaskPayload;
      
      if (!payload || !payload.tenantId || !payload.invoiceId || !payload.providerType) {
        res.status(400).send('Missing required fields in payload');
        return;
      }
      
      const { tenantId, invoiceId, providerType } = payload;
      
      console.log(`Processing push task for invoice ${invoiceId} in tenant ${tenantId}`);
      
      // Get invoice details
      const invoiceDoc = await db.collection('invoices')
        .doc(tenantId)
        .collection('items')
        .doc(invoiceId)
        .get();
      
      if (!invoiceDoc.exists) {
        res.status(404).send(`Invoice ${invoiceId} not found`);
        return;
      }
      
      const invoiceData = invoiceDoc.data();
      
      // Skip if already pushed
      if (invoiceData?.status === 'PUSHED') {
        console.log(`Invoice ${invoiceId} already pushed, skipping`);
        res.status(200).send('Invoice already pushed');
        return;
      }
      
      // Get tenant details for limits
      const tenantDoc = await db.collection('tenants').doc(tenantId).get();
      
      if (!tenantDoc.exists) {
        res.status(404).send(`Tenant ${tenantId} not found`);
        return;
      }
      
      const tenantData = tenantDoc.data();
      
      // Check amount limits based on transaction type
      const transactionType = invoiceData?.type as TransactionType;
      const amount = invoiceData?.totalAmount as number;
      
      if (transactionType === 'BILL' && 
          tenantData?.limits?.maxBillAmt !== null && 
          tenantData?.limits?.maxBillAmt !== undefined && 
          amount > tenantData.limits.maxBillAmt) {
        // Update invoice status to LIMIT_BLOCKED
        await invoiceDoc.ref.update({
          status: 'LIMIT_BLOCKED',
          _updatedAt: new Date().toISOString(),
          _error: {
            code: 'LIMIT_EXCEEDED',
            message: `Bill amount ${amount} exceeds limit ${tenantData.limits.maxBillAmt}`,
            timestamp: new Date().toISOString(),
          },
        });
        
        res.status(200).send('Invoice blocked due to amount limit');
        return;
      } else if (transactionType === 'SALES' && 
          tenantData?.limits?.maxInvoiceAmt !== null && 
          tenantData?.limits?.maxInvoiceAmt !== undefined && 
          amount > tenantData.limits.maxInvoiceAmt) {
        // Update invoice status to LIMIT_BLOCKED
        await invoiceDoc.ref.update({
          status: 'LIMIT_BLOCKED',
          _updatedAt: new Date().toISOString(),
          _error: {
            code: 'LIMIT_EXCEEDED',
            message: `Invoice amount ${amount} exceeds limit ${tenantData.limits.maxInvoiceAmt}`,
            timestamp: new Date().toISOString(),
          },
        });
        
        res.status(200).send('Invoice blocked due to amount limit');
        return;
      }
      
      // Get attachment if available
      let fileBlob = undefined;
      
      if (invoiceData?.attachmentPath) {
        try {
          // Download attachment from GCS
          fileBlob = await downloadAttachment(invoiceData.attachmentPath);
          
          // Compress if needed to meet size requirements
          if (fileBlob.size > MAX_ATTACHMENT_SIZE) {
            try {
              fileBlob = await compressAttachmentIfNeeded(fileBlob);
            } catch (compressError) {
              // Log error but continue without attachment
              console.error(`Failed to compress attachment for ${invoiceId}:`, compressError);
              
              // Add a warning to the invoice
              await invoiceDoc.ref.update({
                _attachmentWarning: {
                  code: 'ATTACHMENT_SIZE_EXCEEDED',
                  message: `Attachment size ${fileBlob.size} exceeds maximum allowed size and could not be compressed`,
                  timestamp: new Date().toISOString(),
                },
              });
              
              // Continue without attachment
              fileBlob = undefined;
            }
          }
        } catch (attachError) {
          // Log error but continue without attachment
          console.error(`Failed to download attachment for ${invoiceId}:`, attachError);
          
          // Add a warning to the invoice
          await invoiceDoc.ref.update({
            _attachmentWarning: {
              code: 'ATTACHMENT_DOWNLOAD_FAILED',
              message: (attachError as Error).message,
              timestamp: new Date().toISOString(),
            },
          });
          
          // Continue without attachment
          fileBlob = undefined;
        }
      }
      
      // Get provider instance
      const provider = getProvider(tenantId, providerType as any);
      
      // Update invoice status to processing
      await invoiceDoc.ref.update({
        _processingStarted: new Date().toISOString(),
      });
      
      // Push to provider based on transaction type
      let result;
      
      if (transactionType === 'BILL') {
        // Push bill to accounting system
        result = await provider.pushBill(invoiceData as any, fileBlob);
      } else {
        // Push sales invoice to accounting system
        result = await provider.pushSalesInvoice(invoiceData as any, fileBlob);
      }
      
      // Update invoice status based on result
      if (result.success) {
        // Update with success status
        await invoiceDoc.ref.update({
          status: 'PUSHED',
          providerIds: {
            [providerType]: result.providerId,
          },
          provider: providerType,
          _updatedAt: new Date().toISOString(),
          _pushedAt: new Date().toISOString(),
        });
        
        console.log(`Successfully pushed ${transactionType.toLowerCase()} ${invoiceId} to ${providerType}`);
        res.status(200).send('Invoice pushed successfully');
      } else {
        // Determine if we should retry
        const shouldRetry = result.error?.retry === true;
        
        // Update with error status
        await invoiceDoc.ref.update({
          status: 'ERROR',
          _error: {
            code: result.error?.code || 'PUSH_FAILED',
            message: result.error?.message || 'Unknown error',
            timestamp: new Date().toISOString(),
            retry: shouldRetry,
          },
          _updatedAt: new Date().toISOString(),
        });
        
        console.error(`Failed to push ${transactionType.toLowerCase()} ${invoiceId} to ${providerType}:`, result.error);
        
        // Return appropriate status code based on whether to retry
        if (shouldRetry) {
          // Return error status to trigger Cloud Tasks retry
          res.status(500).send('Push failed, will retry');
        } else {
          // Return success to acknowledge the task but mark as error in DB
          res.status(200).send('Push failed, not retrying');
        }
      }
    } catch (error) {
      console.error('Error in pushWorker:', error);
      
      // Return error status to trigger Cloud Tasks retry
      res.status(500).send(`Error processing push task: ${(error as Error).message}`);
    }
  });

/**
 * Push a bill to the accounting provider (single transaction)
 * For direct API usage without Cloud Tasks
 */
export async function pushBill(
  tenantId: string,
  billId: string,
  providerType: string = 'qbo'
): Promise<void> {
  try {
    // Call the push worker logic directly
    const payload: PushTaskPayload = {
      tenantId,
      invoiceId: billId,
      providerType,
      idempotencyKey: `bill_${billId}_${Date.now()}`,
    };
    
    // Create a mock request and response
    const mockReq = {
      body: payload,
    } as any;
    
    const mockRes = {
      status: (code: number) => ({
        send: (message: string) => {
          if (code >= 400) {
            throw new Error(message);
          }
        },
      }),
    } as any;
    
    // Call the push worker function directly
    await pushWorkerFunction.run(mockReq, mockRes);
  } catch (error) {
    console.error(`Error pushing bill ${billId}:`, error);
    throw error;
  }
}

/**
 * Push a sales invoice to the accounting provider (single transaction)
 * For direct API usage without Cloud Tasks
 */
export async function pushSalesInvoice(
  tenantId: string,
  invoiceId: string,
  providerType: string = 'qbo'
): Promise<void> {
  try {
    // Call the push worker logic directly
    const payload: PushTaskPayload = {
      tenantId,
      invoiceId,
      providerType,
      idempotencyKey: `invoice_${invoiceId}_${Date.now()}`,
    };
    
    // Create a mock request and response
    const mockReq = {
      body: payload,
    } as any;
    
    const mockRes = {
      status: (code: number) => ({
        send: (message: string) => {
          if (code >= 400) {
            throw new Error(message);
          }
        },
      }),
    } as any;
    
    // Call the push worker function directly
    await pushWorkerFunction.run(mockReq, mockRes);
  } catch (error) {
    console.error(`Error pushing sales invoice ${invoiceId}:`, error);
    throw error;
  }
}
