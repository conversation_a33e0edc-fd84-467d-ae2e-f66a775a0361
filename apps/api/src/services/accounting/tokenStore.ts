/**
 * Accounting Provider Token Store
 * 
 * This module provides secure storage and retrieval of accounting provider authentication tokens.
 * Tokens are stored in Firestore with sensitive values (e.g., refresh tokens) encrypted using
 * Google Cloud KMS before storage. This ensures that even if Firestore data were to be exposed,
 * the tokens would remain protected.
 * 
 * The store supports the multi-tenant architecture of Billsnapp, where each tenant can have
 * connections to different accounting providers. Tokens are stored within tenant documents using
 * a nested structure.
 * 
 * Key security features:
 * - KMS encryption for sensitive tokens
 * - Tenant-level isolation
 * - Automatic refresh token rotation handling
 * 
 * @module accounting/tokenStore
 * @example
 * ```typescript
 * // Store tokens for a QBO provider
 * await tokenStore.updateTokens('tenant-123', 'qbo', {
 *   refreshToken: 'sensitive-refresh-token',
 *   accessToken: 'temporary-access-token',
 *   expiresAt: expiryDate.toISOString(),
 *   status: 'OK'
 * });
 * 
 * // Retrieve tokens
 * const tokens = await tokenStore.getTokens('tenant-123', 'qbo');
 * ```
 */

import { getFirebase } from '../../config/firebase';
import { ProviderType } from './types';
import { encryptWithKMS, decrypt<PERSON>ith<PERSON>MS } from './kmsEncryption';

// Note: Firebase instance will be obtained lazily in functions

/**
 * Provider token information for a tenant
 * 
 * This interface represents the structure of authentication tokens and related information
 * stored for an accounting provider connection. It includes both sensitive data (refreshToken)
 * that requires encryption and non-sensitive metadata.
 * 
 * @interface ProviderTokens
 */
export interface ProviderTokens {
  refreshToken: string;
  accessToken: string;
  expiresAt: string;
  realmId?: string;
  status: 'OK' | 'ERROR';
  lastSyncTime?: string;
}

/**
 * Provider configuration including transaction limits
 * 
 * This interface represents additional configuration options for an accounting provider
 * beyond just the authentication tokens. It includes settings like transaction limits
 * that control business rules for the integration.
 * 
 * @interface ProviderConfig
 */
export interface ProviderConfig {
  limits?: {
    maxBillAmt: number | null;
    maxInvoiceAmt: number | null;
  };
  webhookEnabled?: boolean;
  autoSync?: boolean;
}

/**
 * Retrieve provider tokens for a tenant
 * 
 * @param tenantId - Tenant ID
 * @param providerType - Provider type (qbo, xero, etc.)
 * @returns Provider tokens or null if not found
 */
export async function getProviderTokens(
  tenantId: string,
  providerType: ProviderType
): Promise<ProviderTokens | null> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();
    const db = firebase.firestore;

    const tenantDoc = await db.collection('tenants').doc(tenantId).get();
    
    if (!tenantDoc.exists) {
      console.warn(`Tenant ${tenantId} not found`);
      return null;
    }
    
    const tenantData = tenantDoc.data();
    
    if (!tenantData || !tenantData.providers || !tenantData.providers[providerType]) {
      console.warn(`Provider ${providerType} not configured for tenant ${tenantId}`);
      return null;
    }
    
    const providerData = tenantData.providers[providerType];
    
    // Decrypt the refresh token if it exists
    if (providerData.refreshToken) {
      try {
        providerData.refreshToken = await decryptWithKMS(providerData.refreshToken);
      } catch (decryptError) {
        console.error(`Error decrypting refresh token for tenant ${tenantId}:`, decryptError);
        // Don't stop execution, but return the token as is (it might not be encrypted)
      }
    }
    
    return providerData as ProviderTokens;
  } catch (error) {
    console.error(`Error retrieving provider tokens for tenant ${tenantId}:`, error);
    throw new Error(`Failed to retrieve provider tokens: ${(error as Error).message}`);
  }
}

/**
 * Update provider tokens for a tenant
 * 
 * @param tenantId - Tenant ID
 * @param providerType - Provider type (qbo, xero, etc.)
 * @param tokens - Provider tokens to update
 */
export async function updateProviderTokens(
  tenantId: string,
  providerType: ProviderType,
  tokens: Partial<ProviderTokens>
): Promise<void> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();
    const db = firebase.firestore;

    // Create a copy to avoid modifying the original object
    const tokensCopy = { ...tokens };
    
    // Encrypt the refresh token if it exists
    if (tokensCopy.refreshToken) {
      try {
        tokensCopy.refreshToken = await encryptWithKMS(tokensCopy.refreshToken);
      } catch (encryptError) {
        console.error(`Error encrypting refresh token for tenant ${tenantId}:`, encryptError);
        throw new Error(`Failed to encrypt sensitive data: ${(encryptError as Error).message}`);
      }
    }
    
    // Update the provider tokens in Firestore using dot notation for nested updates
    const updates: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(tokensCopy)) {
      if (value !== undefined) {
        updates[`providers.${providerType}.${key}`] = value;
      }
    }
    
    // Ensure providers object and provider type object exist
    await db.collection('tenants').doc(tenantId).set({
      providers: {
        [providerType]: {}
      }
    }, { merge: true });
    
    // Update with the new values
    await db.collection('tenants').doc(tenantId).update(updates);
    
    console.log(`Updated provider tokens for tenant ${tenantId}`);
  } catch (error) {
    console.error(`Error updating provider tokens for tenant ${tenantId}:`, error);
    throw new Error(`Failed to update provider tokens: ${(error as Error).message}`);
  }
}

/**
 * Remove provider tokens for a tenant
 * 
 * @param tenantId - Tenant ID
 * @param providerType - Provider type (qbo, xero, etc.)
 */
export async function removeProviderTokens(
  tenantId: string,
  providerType: ProviderType
): Promise<void> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();
    const db = firebase.firestore;

    // Remove the provider tokens from Firestore
    await db.collection('tenants').doc(tenantId).update({
      [`providers.${providerType}`]: null
    });
    
    console.log(`Removed provider tokens for tenant ${tenantId}`);
  } catch (error) {
    console.error(`Error removing provider tokens for tenant ${tenantId}:`, error);
    throw new Error(`Failed to remove provider tokens: ${(error as Error).message}`);
  }
}

/**
 * List all tenants with tokens for a specific provider
 * 
 * @param providerType - Provider type (qbo, xero, etc.)
 * @returns Array of tenant IDs
 */
export async function listTenantsWithProvider(
  providerType: ProviderType
): Promise<string[]> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();
    const db = firebase.firestore;

    // Query for tenants with the specified provider
    const snapshot = await db.collection('tenants')
      .where(`providers.${providerType}.status`, '==', 'OK')
      .get();
    
    // Extract tenant IDs
    const tenantIds = snapshot.docs.map((doc: FirebaseFirestore.QueryDocumentSnapshot) => doc.id);
    
    console.log(`Found ${tenantIds.length} tenants with provider ${providerType}`);
    return tenantIds;
  } catch (error) {
    console.error(`Error listing tenants with provider ${providerType}:`, error);
    throw new Error(`Failed to list tenants with provider: ${(error as Error).message}`);
  }
}

/**
 * Get provider configuration for a tenant
 * 
 * @param tenantId - Tenant ID
 * @param providerType - Provider type (qbo, xero, etc.)
 * @returns Provider configuration or null if not found
 */
export async function getProviderConfig(
  tenantId: string,
  providerType: ProviderType
): Promise<ProviderConfig | null> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();
    const db = firebase.firestore;

    const tenantDoc = await db.collection('tenants').doc(tenantId).get();
    
    if (!tenantDoc.exists) {
      console.warn(`Tenant ${tenantId} not found`);
      return null;
    }
    
    const tenantData = tenantDoc.data() || {};
    
    if (!tenantData.providerConfig || !tenantData.providerConfig[providerType]) {
      // Return default empty config if not found
      return {
        limits: {
          maxBillAmt: null,
          maxInvoiceAmt: null
        },
        webhookEnabled: false,
        autoSync: true
      };
    }
    
    return tenantData.providerConfig[providerType] as ProviderConfig;
  } catch (error) {
    console.error(`Error retrieving provider config for tenant ${tenantId}:`, error);
    return null;
  }
}

/**
 * Update provider configuration for a tenant
 * 
 * @param tenantId - Tenant ID
 * @param providerType - Provider type (qbo, xero, etc.)
 * @param config - Provider configuration to update
 */
export async function updateProviderConfig(
  tenantId: string,
  providerType: ProviderType,
  config: Partial<ProviderConfig>
): Promise<void> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();
    const db = firebase.firestore;

    // Use dot notation to update specific provider config
    await db.collection('tenants').doc(tenantId).set({
      providerConfig: {
        [providerType]: config
      }
    }, { merge: true });
    
    console.log(`Updated provider config for tenant ${tenantId}`);
  } catch (error) {
    console.error(`Error updating provider config for tenant ${tenantId}:`, error);
    throw new Error(`Failed to update provider configuration: ${(error as Error).message}`);
  }
}

/**
 * Update last sync time for a tenant
 * 
 * @param tenantId - Tenant ID
 * @param providerType - Provider type (qbo, xero, etc.)
 */
export async function updateLastSyncTime(
  tenantId: string,
  providerType: ProviderType
): Promise<void> {
  try {
    // Get Firebase instance
    const firebase = await getFirebase();
    const db = firebase.firestore;

    const now = new Date().toISOString();

    // Update the last sync time field
    await db.collection('tenants').doc(tenantId).update({
      [`providers.${providerType}.lastSyncTime`]: now
    });
    
    console.log(`Updated last sync time for tenant ${tenantId}`);
  } catch (error) {
    console.error(`Error updating last sync time for tenant ${tenantId}:`, error);
    // Don't throw error as this is non-critical
  }
}

/**
 * Unified token store API
 * 
 * This object exports all token store functions as a single cohesive API, making it
 * easier to import and use throughout the application. This pattern simplifies refactoring
 * and provides a clear interface for interacting with the token store.
 * 
 * The functions are organized into logical groups:
 * - Token management: getTokens, updateTokens, deleteTokens
 * - Tenant operations: listTenantsWithProvider
 * - Configuration: getProviderConfig, updateProviderConfig
 * - Sync management: updateLastSyncTime
 * 
 * @type {object}
 */
export const tokenStore = {
  getTokens: getProviderTokens,
  updateTokens: updateProviderTokens,
  deleteTokens: removeProviderTokens,
  listTenantsWithProvider,
  getProviderConfig,
  updateProviderConfig,
  updateLastSyncTime
};
