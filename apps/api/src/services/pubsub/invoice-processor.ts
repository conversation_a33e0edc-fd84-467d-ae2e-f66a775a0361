import { PubSub, Subscription, Message } from '@google-cloud/pubsub';
import { OcrOrchestrator } from '../ocr/orchestrator';
import { logger } from '../../config/logger';
import { env } from '../../config/env';
import { getFirebase } from '../../config/firebase';

// Initialize OCR orchestrator
const ocrOrchestrator = new OcrOrchestrator();

// Initialize Pub/Sub client
const pubsub = new PubSub();

/**
 * Invoice Processor Pub/Sub Subscriber Service
 * 
 * This service listens for invoice processing messages published to a Pub/Sub topic
 * and processes them using the tiered OCR approach.
 * 
 * It's designed to be deployed as a separate Cloud Run service that scales
 * independently based on the processing load.
 */
export class InvoiceProcessorService {
  private subscription: Subscription;
  private isRunning = false;
  
  constructor() {
    // Get the subscription name from environment or use default
    const subscriptionName = env.PUBSUB_INVOICE_PROCESSING_SUBSCRIPTION || 'invoice-processing-subscriber';
    
    // Create the subscription client
    this.subscription = pubsub.subscription(subscriptionName);
    
    // Configure message handler
    this.subscription.on('message', this.handleMessage.bind(this));
    
    // Handle errors
    this.subscription.on('error', error => {
      logger.error('Pub/Sub subscription error:', error);
    });
  }
  
  /**
   * Start listening for messages
   */
  public start(): void {
    if (this.isRunning) {
      logger.warn('Invoice processor service is already running');
      return;
    }
    
    logger.info('Starting invoice processor service');
    this.isRunning = true;
    
    // Flow control settings to prevent overwhelming the service
    this.subscription.setOptions({
      flowControl: {
        maxMessages: 10, // Process up to 10 messages at once
        allowExcessMessages: false
      }
    });
  }
  
  /**
   * Stop listening for messages
   */
  public stop(): void {
    if (!this.isRunning) {
      logger.warn('Invoice processor service is not running');
      return;
    }
    
    logger.info('Stopping invoice processor service');
    this.isRunning = false;
    
    // Close the subscription
    this.subscription.close();
  }
  
  /**
   * Handle incoming message from Pub/Sub
   */
  private async handleMessage(message: Message): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Parse the message data
      const messageData = JSON.parse(message.data.toString());
      
      logger.info(`Processing invoice OCR job: ${messageData.jobId}`);
      
      // Extract message fields
      const {
        jobId,
        invoiceId,
        tenantId,
        imageUrl,
        originalFilename,
        vendorId,
        startingTier,
        forceAllTiers,
        userId
      } = messageData;
      
      // Validate required fields
      if (!invoiceId || !tenantId || !imageUrl) {
        throw new Error('Missing required fields in message');
      }
      
      // Update invoice status in Firestore
      await this.updateInvoiceStatus(invoiceId, {
        status: 'PROCESSING',
        'processingDetails.jobId': jobId,
        'processingDetails.startTime': new Date(),
        'processingDetails.processorInstance': env.INSTANCE_ID || 'unknown'
      });
      
      // Process the invoice with the OCR orchestrator
      const result = await ocrOrchestrator.processInvoice({
        invoiceId,
        tenantId,
        imageUrl,
        originalFilename: originalFilename || 'unknown',
        vendorId,
        startingTier,
        forceAllTiers
      });
      
      // Log processing time
      const processingTime = Date.now() - startTime;
      logger.info(`Completed invoice OCR job ${jobId} in ${processingTime}ms`);
      
      // Update invoice status based on result
      const statusUpdate: Record<string, any> = {
        'processingDetails.completedAt': new Date(),
        'processingDetails.processingTimeMs': processingTime
      };
      
      // Update status based on OCR result
      if (result.success) {
        statusUpdate.status = result.confidence && 
                            Object.values(result.confidence).some(c => c < 0.8) ? 
                              'REVIEW_NEEDED' : 'EXTRACTED';
      } else {
        statusUpdate.status = 'FAILED';
        statusUpdate['processingDetails.error'] = result.error;
      }
      
      await this.updateInvoiceStatus(invoiceId, statusUpdate);
      
      // Acknowledge the message
      message.ack();
      
    } catch (error) {
      logger.error('Error processing message:', error);
      
      // Nack the message to retry
      message.nack();
    }
  }
  
  /**
   * Update invoice status in Firestore
   */
  private async updateInvoiceStatus(
    invoiceId: string,
    updates: Record<string, any>
  ): Promise<void> {
    try {
      // Get Firebase instance
      const firebase = await getFirebase();

      const invoiceRef = firebase.firestore.collection('invoices').doc(invoiceId);
      
      // Create update object with proper paths
      const updateFields = {
        updatedAt: new Date(),
        ...updates
      };
      
      await invoiceRef.update(updateFields);
    } catch (error) {
      logger.error(`Failed to update invoice status ${invoiceId}:`, error);
      // Don't throw here, allow processing to continue
    }
  }
}
