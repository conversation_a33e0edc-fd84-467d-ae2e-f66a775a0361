#!/usr/bin/env node

/**
 * Test script to verify Firebase lazy initialization
 * This script should start without Firebase credentials and only fail when Firebase is actually accessed
 */

console.log('Starting Firebase lazy initialization test...');

// Import the getFirebase function
import { getFirebase } from './dist/config/firebase.js';

console.log('✅ Successfully imported getFirebase function');
console.log('✅ No Firebase initialization occurred during import');

// Test that Firebase is only initialized when accessed
console.log('\nTesting lazy initialization...');

getFirebase()
  .then(() => {
    console.log('❌ Firebase initialized successfully (this should fail without credentials)');
  })
  .catch((error) => {
    console.log('✅ Firebase initialization failed as expected:', error.message);
    console.log('✅ Lazy initialization is working correctly!');
  });
