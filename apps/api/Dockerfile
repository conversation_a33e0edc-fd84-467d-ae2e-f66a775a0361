# Billsnapp API Dockerfile
# Simplified build for M2-04 fix - builds and runs the main TypeScript API server

FROM node:18-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy the pre-built API application
COPY apps/api/dist ./dist
COPY apps/api/package.json ./package.json

# Install only production dependencies directly with npm (simpler than pnpm)
RUN npm install --only=production

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080
ENV HOST=0.0.0.0

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:${PORT}/health || exit 1

# Expose the port
EXPOSE 8080

# Start the API server
CMD ["node", "dist/index.js"]
