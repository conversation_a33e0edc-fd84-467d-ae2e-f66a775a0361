{"name": "@billsnapp/api", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "tsup --watch --onSuccess \"node dist/index.js\"", "build": "tsup", "start": "node dist/index.js", "generate:openapi": "tsx scripts/generate-openapi-static.ts", "lint": "eslint src --ext .ts --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf .turbo node_modules dist", "test": "jest", "test:e2e": "jest --testMatch='**/*.e2e.test.ts' --runInBand", "test:watch": "jest --watch", "test:openapi": "vitest run src/tests/openapi.test.ts"}, "dependencies": {"@billsnapp/shared-types": "workspace:*", "@fastify/cors": "^8.3.0", "@fastify/static": "^8.2.0", "@trpc/server": "next", "fastify": "^4.24.3", "firebase-admin": "^11.11.0", "superjson": "^2.2.1", "zod": "^3.22.4", "dotenv": "^16.3.1", "google-auth-library": "^9.4.1", "@google-cloud/storage": "^7.6.0", "@google-cloud/pubsub": "^4.0.7", "@google-cloud/vertexai": "^0.2.1", "@google-cloud/secret-manager": "^5.0.1", "@google-cloud/vision": "^4.0.1", "@google-cloud/documentai": "^8.2.0", "firebase": "^10.6.0", "firebase-functions": "^4.5.0", "pino": "^8.16.0", "pino-pretty": "^10.2.3", "canvas": "^2.11.2", "node-fetch": "^2.7.0", "axios": "^1.6.2", "sharp": "^0.32.6", "uuid": "^9.0.1", "trpc-to-openapi": "^2.3.1", "swagger-ui-dist": "^5.14.0"}, "devDependencies": {"@billsnapp/eslint-config": "workspace:*", "@billsnapp/tsconfig": "workspace:*", "@jest/globals": "^29.7.0", "@types/jest": "^29.5.8", "@types/node": "^20.9.1", "@types/node-fetch": "^2.6.9", "@types/uuid": "^9.0.7", "eslint": "^8.53.0", "jest": "^29.7.0", "jest-mock": "^29.7.0", "openapi-schema-validator": "^12.1.3", "ts-jest": "^29.1.1", "tsup": "^8.0.1", "typescript": "^5.2.2", "tsx": "^4.19.4", "vitest": "^0.34.6"}}