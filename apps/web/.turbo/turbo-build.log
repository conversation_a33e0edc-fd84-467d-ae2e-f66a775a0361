

> @billsnapp/web@0.0.0 build /Users/<USER>/Documents/GitHub/AiClearBill/apps/web
> tsc && vite build

[96msrc/components/InvoiceCapture/CameraCapture.tsx[0m:[93m108[0m:[93m9[0m - [91merror[0m[90m TS2322: [0mType '{ deviceId: { exact: string; } | undefined; facingMode: string; width: { ideal: number; }; height: { ideal: number; }; aspectRatio: { ideal: number; }; }' is not assignable to type 'boolean | MediaTrackConstraints'.
  Type '{ deviceId: { exact: string; } | undefined; facingMode: string; width: { ideal: number; }; height: { ideal: number; }; aspectRatio: { ideal: number; }; }' is not assignable to type 'MediaTrackConstraints' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Types of property 'deviceId' are incompatible.
      Type '{ exact: string; } | undefined' is not assignable to type 'ConstrainDOMString'.
        Type 'undefined' is not assignable to type 'ConstrainDOMString'.

[7m108[0m         video: {
[7m   [0m [91m        ~~~~~[0m

[96msrc/components/InvoiceCapture/CameraCapture.tsx[0m:[93m131[0m:[93m45[0m - [91merror[0m[90m TS2339: [0mProperty 'torch' does not exist on type 'MediaTrackCapabilities'.

[7m131[0m       setFlashLightAvailable(!!capabilities.torch);
[7m   [0m [91m                                            ~~~~~[0m

[96msrc/components/InvoiceCapture/CameraCapture.tsx[0m:[93m134[0m:[93m36[0m - [91merror[0m[90m TS2339: [0mProperty 'torch' does not exist on type 'MediaTrackCapabilities'.

[7m134[0m       if (useFlash && capabilities.torch) {
[7m   [0m [91m                                   ~~~~~[0m

[96msrc/components/InvoiceCapture/CameraCapture.tsx[0m:[93m136[0m:[93m24[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'torch' does not exist in type 'MediaTrackConstraintSet'.

[7m136[0m           advanced: [{ torch: true }]
[7m   [0m [91m                       ~~~~~[0m

[96msrc/components/InvoiceCapture/CameraCapture.tsx[0m:[93m172[0m:[93m24[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'torch' does not exist in type 'MediaTrackConstraintSet'.

[7m172[0m           advanced: [{ torch: !useFlash }]
[7m   [0m [91m                       ~~~~~[0m

[96msrc/components/invoices/InvoiceDetailDrawer.tsx[0m:[93m133[0m:[93m21[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ invoiceNumber: string; issueDate: Date; dueDate: Date | undefined; amount: { subtotal: number; tax: number; total: number; currency: string; }; referenceNumber: string | undefined; description: string | undefined; notes: string | undefined; vendorId: string | undefined; }' is not assignable to parameter of type 'SetStateAction<Partial<Invoice>>'.
  Type '{ invoiceNumber: string; issueDate: Date; dueDate: Date | undefined; amount: { subtotal: number; tax: number; total: number; currency: string; }; referenceNumber: string | undefined; description: string | undefined; notes: string | undefined; vendorId: string | undefined; }' is not assignable to type 'Partial<Invoice>' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Types of property 'vendorId' are incompatible.
      Type 'string | undefined' is not assignable to type 'string'.
        Type 'undefined' is not assignable to type 'string'.

[7m133[0m       setFormValues({
[7m   [0m [91m                    ~[0m
[7m134[0m         invoiceNumber: invoice.invoiceNumber,
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m146[0m         vendorId: invoice.vendorId
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m147[0m       });
[7m   [0m [91m~~~~~~~[0m

[96msrc/components/invoices/InvoiceDetailDrawer.tsx[0m:[93m188[0m:[93m39[0m - [91merror[0m[90m TS2576: [0mProperty 'generateFieldChanges' does not exist on type 'AuditLogService'. Did you mean to access the static member 'AuditLogService.generateFieldChanges' instead?

[7m188[0m       const changes = auditLogService.generateFieldChanges(originalValues, currentValues, fieldDisplayNames);
[7m   [0m [91m                                      ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/components/invoices/InvoiceDetailDrawer.tsx[0m:[93m206[0m:[93m19[0m - [91merror[0m[90m TS2345: [0mArgument of type '(prev: Partial<Invoice>) => { amount: { subtotal?: number; tax?: number; total?: number; currency?: string; }; id?: string; tenantId?: string; createdById?: string; vendorId?: string; ... 14 more ...; metadata?: Record<...>; }' is not assignable to parameter of type 'SetStateAction<Partial<Invoice>>'.
  Type '(prev: Partial<Invoice>) => { amount: { subtotal?: number; tax?: number; total?: number; currency?: string; }; id?: string; tenantId?: string; createdById?: string; vendorId?: string; ... 14 more ...; metadata?: Record<...>; }' is not assignable to type '(prevState: Partial<Invoice>) => Partial<Invoice>'.
    Call signature return types '{ amount: { subtotal?: number; tax?: number; total?: number; currency?: string; }; id?: string; tenantId?: string; createdById?: string; vendorId?: string; status?: InvoiceStatus; invoiceNumber?: string; ... 12 more ...; metadata?: Record<...>; }' and 'Partial<Invoice>' are incompatible.
      The types of 'amount' are incompatible between these types.
        Type '{ subtotal?: number; tax?: number; total?: number; currency?: string; }' is not assignable to type '{ subtotal: number; tax: number; total: number; currency: string; }'.
          Property 'subtotal' is optional in type '{ subtotal?: number; tax?: number; total?: number; currency?: string; }' but required in type '{ subtotal: number; tax: number; total: number; currency: string; }'.

[7m206[0m     setFormValues(prev => ({
[7m   [0m [91m                  ~~~~~~~~~~[0m
[7m207[0m       ...prev,
[7m   [0m [91m~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m211[0m       }
[7m   [0m [91m~~~~~~~[0m
[7m212[0m     }));
[7m   [0m [91m~~~~~~[0m

[96msrc/components/invoices/InvoiceDetailDrawer.tsx[0m:[93m251[0m:[93m37[0m - [91merror[0m[90m TS2576: [0mProperty 'generateFieldChanges' does not exist on type 'AuditLogService'. Did you mean to access the static member 'AuditLogService.generateFieldChanges' instead?

[7m251[0m     const changes = auditLogService.generateFieldChanges(originalValues, currentValues, fieldDisplayNames);
[7m   [0m [91m                                    ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/components/invoices/InvoiceDetailDrawer.tsx[0m:[93m695[0m:[93m39[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ invoiceNumber: string; issueDate: Date; dueDate: Date | undefined; amount: { subtotal: number; tax: number; total: number; currency: string; }; referenceNumber: string | undefined; description: string | undefined; notes: string | undefined; vendorId: string | undefined; }' is not assignable to parameter of type 'SetStateAction<Partial<Invoice>>'.
  Type '{ invoiceNumber: string; issueDate: Date; dueDate: Date | undefined; amount: { subtotal: number; tax: number; total: number; currency: string; }; referenceNumber: string | undefined; description: string | undefined; notes: string | undefined; vendorId: string | undefined; }' is not assignable to type 'Partial<Invoice>' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Types of property 'vendorId' are incompatible.
      Type 'string | undefined' is not assignable to type 'string'.
        Type 'undefined' is not assignable to type 'string'.

[7m695[0m                         setFormValues({
[7m   [0m [91m                                      ~[0m
[7m696[0m                           invoiceNumber: invoice.invoiceNumber,
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m703[0m                           vendorId: invoice.vendorId
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m704[0m                         });
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/components/invoices/InvoiceUpload.tsx[0m:[93m10[0m:[93m26[0m - [91merror[0m[90m TS2307: [0mCannot find module '@/hooks/use-toast' or its corresponding type declarations.

[7m10[0m import { useToast } from "@/hooks/use-toast";
[7m  [0m [91m                         ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/components/ui/OfflineStatusBar.tsx[0m:[93m19[0m:[93m13[0m - [91merror[0m[90m TS7030: [0mNot all code paths return a value.

[7m19[0m   useEffect(() => {
[7m  [0m [91m            ~~~~~~~[0m

[96msrc/components/ui/sheet.tsx[0m:[93m15[0m:[93m3[0m - [91merror[0m[90m TS2339: [0mProperty 'className' does not exist on type 'DialogPortalProps'.

[7m15[0m   className,
[7m  [0m [91m  ~~~~~~~~~[0m

[96msrc/components/ui/sheet.tsx[0m:[93m18[0m:[93m26[0m - [91merror[0m[90m TS2375: [0mType '{ children?: ReactNode; container?: Element | DocumentFragment | null | undefined; forceMount?: true; className: string; }' is not assignable to type 'IntrinsicAttributes & DialogPortalProps' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Property 'className' does not exist on type 'IntrinsicAttributes & DialogPortalProps'.

[7m18[0m   <SheetPrimitive.Portal className={cn(className)} {...props} />
[7m  [0m [91m                         ~~~~~~~~~[0m

[96msrc/components/ui/use-toast.ts[0m:[93m188[0m:[93m45[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ type: "DISMISS_TOAST"; toastId: string | undefined; }' is not assignable to parameter of type 'Action'.
  Type '{ type: "DISMISS_TOAST"; toastId: string | undefined; }' is not assignable to type '{ type: "DISMISS_TOAST"; toastId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Types of property 'toastId' are incompatible.
      Type 'string | undefined' is not assignable to type 'string'.
        Type 'undefined' is not assignable to type 'string'.

[7m188[0m     dismiss: (toastId?: string) => dispatch({ type: "DISMISS_TOAST", toastId }),
[7m   [0m [91m                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/contexts/AuthContext.tsx[0m:[93m31[0m:[93m23[0m - [91merror[0m[90m TS2339: [0mProperty 'env' does not exist on type 'ImportMeta'.

[7m31[0m   apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
[7m  [0m [91m                      ~~~[0m

[96msrc/contexts/AuthContext.tsx[0m:[93m32[0m:[93m27[0m - [91merror[0m[90m TS2339: [0mProperty 'env' does not exist on type 'ImportMeta'.

[7m32[0m   authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
[7m  [0m [91m                          ~~~[0m

[96msrc/contexts/AuthContext.tsx[0m:[93m33[0m:[93m26[0m - [91merror[0m[90m TS2339: [0mProperty 'env' does not exist on type 'ImportMeta'.

[7m33[0m   projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
[7m  [0m [91m                         ~~~[0m

[96msrc/contexts/AuthContext.tsx[0m:[93m34[0m:[93m30[0m - [91merror[0m[90m TS2339: [0mProperty 'env' does not exist on type 'ImportMeta'.

[7m34[0m   storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
[7m  [0m [91m                             ~~~[0m

[96msrc/contexts/AuthContext.tsx[0m:[93m35[0m:[93m34[0m - [91merror[0m[90m TS2339: [0mProperty 'env' does not exist on type 'ImportMeta'.

[7m35[0m   messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
[7m  [0m [91m                                 ~~~[0m

[96msrc/contexts/AuthContext.tsx[0m:[93m36[0m:[93m22[0m - [91merror[0m[90m TS2339: [0mProperty 'env' does not exist on type 'ImportMeta'.

[7m36[0m   appId: import.meta.env.VITE_FIREBASE_APP_ID
[7m  [0m [91m                     ~~~[0m

[96msrc/contexts/AuthContext.tsx[0m:[93m82[0m:[93m39[0m - [91merror[0m[90m TS2339: [0mProperty 'auth' does not exist on type '"The property 'useContext' in your router collides with a built-in method, rename this router or procedure on your backend." | "The property 'useUtils' in your router collides with a built-in method, rename this router or procedure on your backend." | "The property 'Provider' in your router collides with a built-in ...'.
  Property 'auth' does not exist on type '"The property 'useContext' in your router collides with a built-in method, rename this router or procedure on your backend."'.

[7m82[0m   const getUserProfileMutation = trpc.auth.getUser.useMutation();
[7m  [0m [91m                                      ~~~~[0m

[96msrc/contexts/AuthContext.tsx[0m:[93m225[0m:[93m12[0m - [91merror[0m[90m TS7053: [0mElement implicitly has an 'any' type because expression of type 'UserRole' can't be used to index type '{ invoice_capturer: number; approver: number; accountant: number; tenant_admin: number; system_admin: number; }'.
  Property '[UserRole.SNAPPER]' does not exist on type '{ invoice_capturer: number; approver: number; accountant: number; tenant_admin: number; system_admin: number; }'.

[7m225[0m     return roleLevel[userProfile.role] >= roleLevel[requiredRole];
[7m   [0m [91m           ~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/contexts/AuthContext.tsx[0m:[93m225[0m:[93m43[0m - [91merror[0m[90m TS7053: [0mElement implicitly has an 'any' type because expression of type 'UserRole' can't be used to index type '{ invoice_capturer: number; approver: number; accountant: number; tenant_admin: number; system_admin: number; }'.
  Property '[UserRole.SNAPPER]' does not exist on type '{ invoice_capturer: number; approver: number; accountant: number; tenant_admin: number; system_admin: number; }'.

[7m225[0m     return roleLevel[userProfile.role] >= roleLevel[requiredRole];
[7m   [0m [91m                                          ~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/contexts/TenantContext.tsx[0m:[93m31[0m:[93m31[0m - [91merror[0m[90m TS2339: [0mProperty 'tenants' does not exist on type '"The property 'useContext' in your router collides with a built-in method, rename this router or procedure on your backend." | "The property 'useUtils' in your router collides with a built-in method, rename this router or procedure on your backend." | "The property 'Provider' in your router collides with a built-in ...'.
  Property 'tenants' does not exist on type '"The property 'useContext' in your router collides with a built-in method, rename this router or procedure on your backend."'.

[7m31[0m   const getTenantQuery = trpc.tenants.getTenant.useMutation();
[7m  [0m [91m                              ~~~~~~~[0m

[96msrc/contexts/TenantContext.tsx[0m:[93m32[0m:[93m39[0m - [91merror[0m[90m TS2339: [0mProperty 'tenants' does not exist on type '"The property 'useContext' in your router collides with a built-in method, rename this router or procedure on your backend." | "The property 'useUtils' in your router collides with a built-in method, rename this router or procedure on your backend." | "The property 'Provider' in your router collides with a built-in ...'.
  Property 'tenants' does not exist on type '"The property 'useContext' in your router collides with a built-in method, rename this router or procedure on your backend."'.

[7m32[0m   const updateSettingsMutation = trpc.tenants.updateSettings.useMutation();
[7m  [0m [91m                                      ~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m10[0m:[93m27[0m - [91merror[0m[90m TS2305: [0mModule '"@billsnapp/shared-types"' has no exported member 'DEFAULT_USER_PREFERENCES'.

[7m10[0m import { UserPreferences, DEFAULT_USER_PREFERENCES } from '@billsnapp/shared-types';
[7m  [0m [91m                          ~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m32[0m:[93m11[0m - [91merror[0m[90m TS2339: [0mProperty 'user' does not exist on type 'AuthContextType'.

[7m32[0m   const { user } = useAuth();
[7m  [0m [91m          ~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m88[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'dashboard' does not exist in type 'Partial<UserPreferences>'.

[7m88[0m       dashboard: {
[7m  [0m [91m      ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m89[0m:[93m24[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m89[0m         ...preferences.dashboard,
[7m  [0m [91m                       ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m97[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'ui' does not exist in type 'Partial<UserPreferences>'.

[7m97[0m       ui: {
[7m  [0m [91m      ~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m98[0m:[93m24[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m98[0m         ...preferences.ui,
[7m  [0m [91m                       ~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m106[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'dashboard' does not exist in type 'Partial<UserPreferences>'.

[7m106[0m       dashboard: {
[7m   [0m [91m      ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m107[0m:[93m24[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m107[0m         ...preferences.dashboard,
[7m   [0m [91m                       ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m115[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'dashboard' does not exist in type 'Partial<UserPreferences>'.

[7m115[0m       dashboard: {
[7m   [0m [91m      ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m116[0m:[93m24[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m116[0m         ...preferences.dashboard,
[7m   [0m [91m                       ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m118[0m:[93m26[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m118[0m           ...preferences.dashboard.columnVisibility,
[7m   [0m [91m                         ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m127[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'dashboard' does not exist in type 'Partial<UserPreferences>'.

[7m127[0m       dashboard: {
[7m   [0m [91m      ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m128[0m:[93m24[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m128[0m         ...preferences.dashboard,
[7m   [0m [91m                       ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m136[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'dashboard' does not exist in type 'Partial<UserPreferences>'.

[7m136[0m       dashboard: {
[7m   [0m [91m      ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m137[0m:[93m24[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m137[0m         ...preferences.dashboard,
[7m   [0m [91m                       ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m145[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'dashboard' does not exist in type 'Partial<UserPreferences>'.

[7m145[0m       dashboard: {
[7m   [0m [91m      ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m146[0m:[93m24[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m146[0m         ...preferences.dashboard,
[7m   [0m [91m                       ~~~~~~~~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m154[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'ui' does not exist in type 'Partial<UserPreferences>'.

[7m154[0m       ui: {
[7m   [0m [91m      ~~[0m

[96msrc/contexts/UserPreferencesContext.tsx[0m:[93m155[0m:[93m24[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m155[0m         ...preferences.ui,
[7m   [0m [91m                       ~~[0m

[96msrc/hooks/useImageQualityCheck.ts[0m:[93m186[0m:[93m34[0m - [91merror[0m[90m TS2345: [0mArgument of type 'Tensor<Rank>' is not assignable to parameter of type 'Tensor3D | TensorLike | Tensor4D'.
  Type 'Tensor<Rank>' is not assignable to type 'Tensor3D | Tensor4D' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type 'Tensor<Rank>' is not assignable to type 'Tensor3D' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Type 'Rank' is not assignable to type 'Rank.R3'.

[7m186[0m       const filtered = tf.conv2d(expanded, laplacian.reshape([3, 3, 1, 1]), 1, 'same');
[7m   [0m [91m                                 ~~~~~~~~[0m

[96msrc/lib/api/apiClient.ts[0m:[93m15[0m:[93m11[0m - [91merror[0m[90m TS2339: [0mProperty 'user' does not exist on type 'AuthContextType'.

[7m15[0m   const { user, idToken } = useAuth();
[7m  [0m [91m          ~~~~[0m

[96msrc/lib/api/apiClient.ts[0m:[93m71[0m:[93m42[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ id: string; _localId: string; _syncStatus: "pending"; tenantId: string; vendorId: string | null; invoiceNumber: string; issueDate: Date; dueDate: Date | null; amount: number; ... 7 more ...; updatedAt: Date; }' is not assignable to parameter of type 'OfflineInvoice'.
  Type '{ id: string; _localId: string; _syncStatus: "pending"; tenantId: string; vendorId: string | null; invoiceNumber: string; issueDate: Date; dueDate: Date | null; amount: number; ... 7 more ...; updatedAt: Date; }' is missing the following properties from type 'OfflineInvoice': vatLines, lineItems, attachments, processingDetails

[7m71[0m         const id = await db.invoices.add(offlineInvoice);
[7m  [0m [91m                                         ~~~~~~~~~~~~~~[0m

[96msrc/lib/api/apiClient.ts[0m:[93m84[0m:[93m18[0m - [91merror[0m[90m TS2783: [0m'id' is specified more than once, so this usage will be overwritten.

[7m84[0m         return { id, ...offlineInvoice };
[7m  [0m [91m                 ~~[0m

  [96msrc/lib/api/apiClient.ts[0m:[93m84[0m:[93m22[0m
    [7m84[0m         return { id, ...offlineInvoice };
    [7m  [0m [96m                     ~~~~~~~~~~~~~~~~~[0m
    This spread always overwrites this property.

[96msrc/lib/api/apiClient.ts[0m:[93m145[0m:[93m41[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ id: string; _localId: string; _syncStatus: "pending"; tenantId: string; name: string; taxNumber: string | null; address: string | null; contactInfo: { email?: string; phone?: string; website?: string; } | null; ... 5 more ...; createdById: any; }' is not assignable to parameter of type 'OfflineVendor'.
  Types of property 'taxNumber' are incompatible.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.

[7m145[0m         const id = await db.vendors.add(offlineVendor);
[7m   [0m [91m                                        ~~~~~~~~~~~~~[0m

[96msrc/lib/api/apiClient.ts[0m:[93m158[0m:[93m18[0m - [91merror[0m[90m TS2783: [0m'id' is specified more than once, so this usage will be overwritten.

[7m158[0m         return { id, ...offlineVendor };
[7m   [0m [91m                 ~~[0m

  [96msrc/lib/api/apiClient.ts[0m:[93m158[0m:[93m22[0m
    [7m158[0m         return { id, ...offlineVendor };
    [7m   [0m [96m                     ~~~~~~~~~~~~~~~~[0m
    This spread always overwrites this property.

[96msrc/lib/api/apiClient.ts[0m:[93m220[0m:[93m11[0m - [91merror[0m[90m TS7006: [0mParameter 'online' implicitly has an 'any' type.

[7m220[0m           online => !offlineInvoices.some(offline => offline.id === online.id)
[7m   [0m [91m          ~~~~~~[0m

[96msrc/lib/api/apiClient.ts[0m:[93m297[0m:[93m11[0m - [91merror[0m[90m TS7006: [0mParameter 'online' implicitly has an 'any' type.

[7m297[0m           online => !offlineVendors.some(offline => offline.id === online.id)
[7m   [0m [91m          ~~~~~~[0m

[96msrc/lib/db.ts[0m:[93m245[0m:[93m14[0m - [91merror[0m[90m TS2339: [0mProperty 'initializeDatabase' does not exist on type 'BillsNappDatabase'.

[7m245[0m     await db.initializeDatabase();
[7m   [0m [91m             ~~~~~~~~~~~~~~~~~~[0m

[96msrc/lib/logger.ts[0m:[93m13[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'env' does not exist on type 'ImportMeta'.

[7m13[0m     this.isDevelopment = import.meta.env.DEV || import.meta.env.MODE === 'development';
[7m  [0m [91m                                     ~~~[0m

[96msrc/lib/logger.ts[0m:[93m13[0m:[93m61[0m - [91merror[0m[90m TS2339: [0mProperty 'env' does not exist on type 'ImportMeta'.

[7m13[0m     this.isDevelopment = import.meta.env.DEV || import.meta.env.MODE === 'development';
[7m  [0m [91m                                                            ~~~[0m

[96msrc/lib/trpc.tsx[0m:[93m6[0m:[93m32[0m - [91merror[0m[90m TS2307: [0mCannot find module '@billsnapp/api' or its corresponding type declarations.

[7m6[0m import type { AppRouter } from '@billsnapp/api'; // This will be created in the API package
[7m [0m [91m                               ~~~~~~~~~~~~~~~~[0m

[96msrc/lib/trpc.tsx[0m:[93m13[0m:[93m3[0m - [91merror[0m[90m TS2322: [0mType 'typeof SuperJSON' is not assignable to type 'TypeError<"The transformer property has moved to httpLink/httpBatchLink/wsLink">'.
  Type 'typeof SuperJSON' is not assignable to type '"The transformer property has moved to httpLink/httpBatchLink/wsLink"'.

[7m13[0m   transformer: superjson,
[7m  [0m [91m  ~~~~~~~~~~~[0m

  [96m../../node_modules/.pnpm/@trpc+client@11.0.0_@trpc+server@11.0.0_typescript@5.8.3__typescript@5.8.3/node_modules/@trpc/client/dist/internals/TRPCUntypedClient.d.ts[0m:[93m26[0m:[93m5[0m
    [7m26[0m     transformer?: TypeError<'The transformer property has moved to httpLink/httpBatchLink/wsLink'>;
    [7m  [0m [96m    ~~~~~~~~~~~[0m
    The expected type comes from property 'transformer' which is declared here on type 'CreateTRPCClientOptions<AppRouter>'

[96msrc/lib/trpc.tsx[0m:[93m16[0m:[93m27[0m - [91merror[0m[90m TS2339: [0mProperty 'env' does not exist on type 'ImportMeta'.

[7m16[0m       url: `${import.meta.env.VITE_API_URL || 'http://localhost:4000'}/trpc`,
[7m  [0m [91m                          ~~~[0m

[96msrc/lib/trpc.tsx[0m:[93m39[0m:[93m10[0m - [91merror[0m[90m TS2339: [0mProperty 'createClient' does not exist on type '"The property 'useContext' in your router collides with a built-in method, rename this router or procedure on your backend." | "The property 'useUtils' in your router collides with a built-in method, rename this router or procedure on your backend." | "The property 'Provider' in your router collides with a built-in ...'.
  Property 'createClient' does not exist on type '"The property 'useContext' in your router collides with a built-in method, rename this router or procedure on your backend."'.

[7m39[0m     trpc.createClient({
[7m  [0m [91m         ~~~~~~~~~~~~[0m

[96msrc/lib/trpc.tsx[0m:[93m43[0m:[93m31[0m - [91merror[0m[90m TS2339: [0mProperty 'env' does not exist on type 'ImportMeta'.

[7m43[0m           url: `${import.meta.env.VITE_API_URL || 'http://localhost:4000'}/trpc`,
[7m  [0m [91m                              ~~~[0m

[96msrc/lib/trpc.tsx[0m:[93m57[0m:[93m11[0m - [91merror[0m[90m TS2339: [0mProperty 'Provider' does not exist on type '"The property 'useContext' in your router collides with a built-in method, rename this router or procedure on your backend." | "The property 'useUtils' in your router collides with a built-in method, rename this router or procedure on your backend." | "The property 'Provider' in your router collides with a built-in ...'.
  Property 'Provider' does not exist on type '"The property 'useContext' in your router collides with a built-in method, rename this router or procedure on your backend."'.

[7m57[0m     <trpc.Provider client={trpcClient} queryClient={queryClient}>
[7m  [0m [91m          ~~~~~~~~[0m

[96msrc/lib/trpc.tsx[0m:[93m61[0m:[93m12[0m - [91merror[0m[90m TS2339: [0mProperty 'Provider' does not exist on type '"The property 'useContext' in your router collides with a built-in method, rename this router or procedure on your backend." | "The property 'useUtils' in your router collides with a built-in method, rename this router or procedure on your backend." | "The property 'Provider' in your router collides with a built-in ...'.
  Property 'Provider' does not exist on type '"The property 'useContext' in your router collides with a built-in method, rename this router or procedure on your backend."'.

[7m61[0m     </trpc.Provider>
[7m  [0m [91m           ~~~~~~~~[0m

[96msrc/main.tsx[0m:[93m4[0m:[93m28[0m - [91merror[0m[90m TS2307: [0mCannot find module 'virtual:pwa-register' or its corresponding type declarations.

[7m4[0m import { registerSW } from 'virtual:pwa-register';
[7m [0m [91m                           ~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/admin/AdminPanel.tsx[0m:[93m9[0m:[93m11[0m - [91merror[0m[90m TS2339: [0mProperty 'user' does not exist on type 'AuthContextType'.

[7m9[0m   const { user } = useAuth();
[7m [0m [91m          ~~~~[0m

[96msrc/pages/admin/TenantManagement.tsx[0m:[93m9[0m:[93m132[0m - [91merror[0m[90m TS2307: [0mCannot find module '@/components/ui/dropdown-menu' or its corresponding type declarations.

[7m9[0m import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
[7m [0m [91m                                                                                                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/admin/UserManagement.tsx[0m:[93m9[0m:[93m132[0m - [91merror[0m[90m TS2307: [0mCannot find module '@/components/ui/dropdown-menu' or its corresponding type declarations.

[7m9[0m import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
[7m [0m [91m                                                                                                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/AuditLogs.tsx[0m:[93m21[0m:[93m8[0m - [91merror[0m[90m TS2307: [0mCannot find module '@/components/ui/pagination' or its corresponding type declarations.

[7m21[0m } from '@/components/ui/pagination';
[7m  [0m [91m       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/InvoiceCapture.tsx[0m:[93m9[0m:[93m27[0m - [91merror[0m[90m TS2307: [0mCannot find module '@/components/ui/separator' or its corresponding type declarations.

[7m9[0m import { Separator } from "@/components/ui/separator";
[7m [0m [91m                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/InvoiceCapture.tsx[0m:[93m10[0m:[93m26[0m - [91merror[0m[90m TS2307: [0mCannot find module '@/hooks/use-toast' or its corresponding type declarations.

[7m10[0m import { useToast } from "@/hooks/use-toast";
[7m  [0m [91m                         ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/InvoiceDetails.tsx[0m:[93m7[0m:[93m27[0m - [91merror[0m[90m TS2307: [0mCannot find module '@/components/ui/separator' or its corresponding type declarations.

[7m7[0m import { Separator } from "@/components/ui/separator";
[7m [0m [91m                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/InvoiceDetails.tsx[0m:[93m113[0m:[93m16[0m - [91merror[0m[90m TS2322: [0mType '"warning"' is not assignable to type '"default" | "destructive" | null | undefined'.

[7m113[0m         <Alert variant="warning" className="bg-yellow-50 border-yellow-200">
[7m   [0m [91m               ~~~~~~~[0m

  [96msrc/components/ui/alert.tsx[0m:[93m9[0m:[93m7[0m
    [7m  9[0m       variant: {
    [7m   [0m [96m      ~~~~~~~~~~[0m
    [7m 10[0m         default: "bg-background text-foreground",
    [7m   [0m [96m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
    [7m...[0m 
    [7m 12[0m           "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",
    [7m   [0m [96m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
    [7m 13[0m       },
    [7m   [0m [96m~~~~~~~[0m
    The expected type comes from property 'variant' which is declared here on type 'IntrinsicAttributes & HTMLAttributes<HTMLDivElement> & VariantProps<(props?: (ConfigVariants<{ variant: { default: string; destructive: string; }; }> & ClassProp) | undefined) => string> & RefAttributes<...>'

[96msrc/pages/Settings.tsx[0m:[93m6[0m:[93m44[0m - [91merror[0m[90m TS2307: [0mCannot find module '@/components/ui/radio-group' or its corresponding type declarations.

[7m6[0m import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
[7m [0m [91m                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m42[0m:[93m57[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m42[0m   const handleUiToggle = (key: keyof typeof preferences.ui) => {
[7m  [0m [91m                                                        ~~[0m

[96msrc/pages/Settings.tsx[0m:[93m43[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m43[0m     if (typeof preferences.ui[key] === 'boolean') {
[7m  [0m [91m                           ~~[0m

[96msrc/pages/Settings.tsx[0m:[93m45[0m:[93m9[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'ui' does not exist in type 'Partial<UserPreferences>'.

[7m45[0m         ui: {
[7m  [0m [91m        ~~[0m

[96msrc/pages/Settings.tsx[0m:[93m46[0m:[93m26[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m46[0m           ...preferences.ui,
[7m  [0m [91m                         ~~[0m

[96msrc/pages/Settings.tsx[0m:[93m47[0m:[93m31[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m47[0m           [key]: !preferences.ui[key]
[7m  [0m [91m                              ~~[0m

[96msrc/pages/Settings.tsx[0m:[93m87[0m:[93m35[0m - [91merror[0m[90m TS7006: [0mParameter 'value' implicitly has an 'any' type.

[7m87[0m                   onValueChange={(value) => handleThemeChange(value as 'light' | 'dark' | 'system')}
[7m  [0m [91m                                  ~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m118[0m:[93m42[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m118[0m                       value={preferences.ui.tableDisplayDensity}
[7m   [0m [91m                                         ~~[0m

[96msrc/pages/Settings.tsx[0m:[93m136[0m:[93m44[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m136[0m                       checked={preferences.ui.compactMode}
[7m   [0m [91m                                           ~~[0m

[96msrc/pages/Settings.tsx[0m:[93m145[0m:[93m44[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m145[0m                       checked={preferences.ui.animationsEnabled}
[7m   [0m [91m                                           ~~[0m

[96msrc/pages/Settings.tsx[0m:[93m154[0m:[93m44[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m154[0m                       checked={preferences.ui.highContrastMode}
[7m   [0m [91m                                           ~~[0m

[96msrc/pages/Settings.tsx[0m:[93m180[0m:[93m42[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m180[0m                       value={preferences.dashboard.defaultView}
[7m   [0m [91m                                         ~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m181[0m:[93m39[0m - [91merror[0m[90m TS7006: [0mParameter 'value' implicitly has an 'any' type.

[7m181[0m                       onValueChange={(value) => {
[7m   [0m [91m                                      ~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m183[0m:[93m27[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'dashboard' does not exist in type 'Partial<UserPreferences>'.

[7m183[0m                           dashboard: {
[7m   [0m [91m                          ~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m184[0m:[93m44[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m184[0m                             ...preferences.dashboard,
[7m   [0m [91m                                           ~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m209[0m:[93m42[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m209[0m                       value={preferences.dashboard.pageSize.toString()}
[7m   [0m [91m                                         ~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m212[0m:[93m27[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'dashboard' does not exist in type 'Partial<UserPreferences>'.

[7m212[0m                           dashboard: {
[7m   [0m [91m                          ~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m213[0m:[93m44[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m213[0m                             ...preferences.dashboard,
[7m   [0m [91m                                           ~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m237[0m:[93m44[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m237[0m                       checked={preferences.dashboard.showArchivedInvoices}
[7m   [0m [91m                                           ~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m240[0m:[93m27[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'dashboard' does not exist in type 'Partial<UserPreferences>'.

[7m240[0m                           dashboard: {
[7m   [0m [91m                          ~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m241[0m:[93m44[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m241[0m                             ...preferences.dashboard,
[7m   [0m [91m                                           ~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m278[0m:[93m56[0m - [91merror[0m[90m TS2339: [0mProperty 'browser' does not exist on type '{ email: boolean; push: boolean; inApp: boolean; }'.

[7m278[0m                     checked={preferences.notifications.browser}
[7m   [0m [91m                                                       ~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m279[0m:[93m69[0m - [91merror[0m[90m TS2345: [0mArgument of type '"browser"' is not assignable to parameter of type '"email" | "push" | "inApp"'.

[7m279[0m                     onCheckedChange={() => handleNotificationToggle('browser')}
[7m   [0m [91m                                                                    ~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m291[0m:[93m60[0m - [91merror[0m[90m TS2339: [0mProperty 'invoiceApproved' does not exist on type '{ email: boolean; push: boolean; inApp: boolean; }'.

[7m291[0m                         checked={preferences.notifications.invoiceApproved}
[7m   [0m [91m                                                           ~~~~~~~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m292[0m:[93m73[0m - [91merror[0m[90m TS2345: [0mArgument of type '"invoiceApproved"' is not assignable to parameter of type '"email" | "push" | "inApp"'.

[7m292[0m                         onCheckedChange={() => handleNotificationToggle('invoiceApproved')}
[7m   [0m [91m                                                                        ~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m300[0m:[93m60[0m - [91merror[0m[90m TS2339: [0mProperty 'invoiceRejected' does not exist on type '{ email: boolean; push: boolean; inApp: boolean; }'.

[7m300[0m                         checked={preferences.notifications.invoiceRejected}
[7m   [0m [91m                                                           ~~~~~~~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m301[0m:[93m73[0m - [91merror[0m[90m TS2345: [0mArgument of type '"invoiceRejected"' is not assignable to parameter of type '"email" | "push" | "inApp"'.

[7m301[0m                         onCheckedChange={() => handleNotificationToggle('invoiceRejected')}
[7m   [0m [91m                                                                        ~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m309[0m:[93m60[0m - [91merror[0m[90m TS2339: [0mProperty 'newComment' does not exist on type '{ email: boolean; push: boolean; inApp: boolean; }'.

[7m309[0m                         checked={preferences.notifications.newComment}
[7m   [0m [91m                                                           ~~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m310[0m:[93m73[0m - [91merror[0m[90m TS2345: [0mArgument of type '"newComment"' is not assignable to parameter of type '"email" | "push" | "inApp"'.

[7m310[0m                         onCheckedChange={() => handleNotificationToggle('newComment')}
[7m   [0m [91m                                                                        ~~~~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m318[0m:[93m60[0m - [91merror[0m[90m TS2339: [0mProperty 'mentionedInComment' does not exist on type '{ email: boolean; push: boolean; inApp: boolean; }'.

[7m318[0m                         checked={preferences.notifications.mentionedInComment}
[7m   [0m [91m                                                           ~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m319[0m:[93m73[0m - [91merror[0m[90m TS2345: [0mArgument of type '"mentionedInComment"' is not assignable to parameter of type '"email" | "push" | "inApp"'.

[7m319[0m                         onCheckedChange={() => handleNotificationToggle('mentionedInComment')}
[7m   [0m [91m                                                                        ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m327[0m:[93m60[0m - [91merror[0m[90m TS2339: [0mProperty 'dailyDigest' does not exist on type '{ email: boolean; push: boolean; inApp: boolean; }'.

[7m327[0m                         checked={preferences.notifications.dailyDigest}
[7m   [0m [91m                                                           ~~~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m328[0m:[93m73[0m - [91merror[0m[90m TS2345: [0mArgument of type '"dailyDigest"' is not assignable to parameter of type '"email" | "push" | "inApp"'.

[7m328[0m                         onCheckedChange={() => handleNotificationToggle('dailyDigest')}
[7m   [0m [91m                                                                        ~~~~~~~~~~~~~[0m

[96msrc/pages/Settings.tsx[0m:[93m364[0m:[93m51[0m - [91merror[0m[90m TS2339: [0mProperty 'updatedAt' does not exist on type 'UserPreferences'.

[7m364[0m               Last updated: {new Date(preferences.updatedAt).toLocaleString()}
[7m   [0m [91m                                                  ~~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m13[0m:[93m27[0m - [91merror[0m[90m TS2307: [0mCannot find module '@/components/ui/separator' or its corresponding type declarations.

[7m13[0m import { Separator } from "@/components/ui/separator";
[7m  [0m [91m                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m387[0m:[93m81[0m - [91merror[0m[90m TS2339: [0mProperty 'bankInfo' does not exist on type '{ id: string; name: string; email: string; phone: string; totalSpent: number; lastInvoice: string; status: string; taxId: string; paymentTerms: string; address: { street: string; city: string; state: string; zipCode: string; country: string; }; ... 7 more ...; invoiceHistory: { ...; }[]; }'.

[7m387[0m                     {vendor.preferredPaymentMethod === "ACH Transfer" && vendor.bankInfo && (
[7m   [0m [91m                                                                                ~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m391[0m:[93m72[0m - [91merror[0m[90m TS2339: [0mProperty 'bankInfo' does not exist on type '{ id: string; name: string; email: string; phone: string; totalSpent: number; lastInvoice: string; status: string; taxId: string; paymentTerms: string; address: { street: string; city: string; state: string; zipCode: string; country: string; }; ... 7 more ...; invoiceHistory: { ...; }[]; }'.

[7m391[0m                           <Input id="accountName" defaultValue={vendor.bankInfo.accountName} />
[7m   [0m [91m                                                                       ~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m396[0m:[93m76[0m - [91merror[0m[90m TS2339: [0mProperty 'bankInfo' does not exist on type '{ id: string; name: string; email: string; phone: string; totalSpent: number; lastInvoice: string; status: string; taxId: string; paymentTerms: string; address: { street: string; city: string; state: string; zipCode: string; country: string; }; ... 7 more ...; invoiceHistory: { ...; }[]; }'.

[7m396[0m                             <Input id="accountNumber" defaultValue={vendor.bankInfo.accountNumber} />
[7m   [0m [91m                                                                           ~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m400[0m:[93m76[0m - [91merror[0m[90m TS2339: [0mProperty 'bankInfo' does not exist on type '{ id: string; name: string; email: string; phone: string; totalSpent: number; lastInvoice: string; status: string; taxId: string; paymentTerms: string; address: { street: string; city: string; state: string; zipCode: string; country: string; }; ... 7 more ...; invoiceHistory: { ...; }[]; }'.

[7m400[0m                             <Input id="routingNumber" defaultValue={vendor.bankInfo.routingNumber} />
[7m   [0m [91m                                                                           ~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m405[0m:[93m69[0m - [91merror[0m[90m TS2339: [0mProperty 'bankInfo' does not exist on type '{ id: string; name: string; email: string; phone: string; totalSpent: number; lastInvoice: string; status: string; taxId: string; paymentTerms: string; address: { street: string; city: string; state: string; zipCode: string; country: string; }; ... 7 more ...; invoiceHistory: { ...; }[]; }'.

[7m405[0m                           <Input id="bankName" defaultValue={vendor.bankInfo.bankName} />
[7m   [0m [91m                                                                    ~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m422[0m:[93m81[0m - [91merror[0m[90m TS2339: [0mProperty 'bankInfo' does not exist on type '{ id: string; name: string; email: string; phone: string; totalSpent: number; lastInvoice: string; status: string; taxId: string; paymentTerms: string; address: { street: string; city: string; state: string; zipCode: string; country: string; }; ... 7 more ...; invoiceHistory: { ...; }[]; }'.

[7m422[0m                     {vendor.preferredPaymentMethod === "ACH Transfer" && vendor.bankInfo && (
[7m   [0m [91m                                                                                ~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m427[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'bankInfo' does not exist on type '{ id: string; name: string; email: string; phone: string; totalSpent: number; lastInvoice: string; status: string; taxId: string; paymentTerms: string; address: { street: string; city: string; state: string; zipCode: string; country: string; }; ... 7 more ...; invoiceHistory: { ...; }[]; }'.

[7m427[0m                           <p>{vendor.bankInfo.accountName}</p>
[7m   [0m [91m                                     ~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m428[0m:[93m67[0m - [91merror[0m[90m TS2339: [0mProperty 'bankInfo' does not exist on type '{ id: string; name: string; email: string; phone: string; totalSpent: number; lastInvoice: string; status: string; taxId: string; paymentTerms: string; address: { street: string; city: string; state: string; zipCode: string; country: string; }; ... 7 more ...; invoiceHistory: { ...; }[]; }'.

[7m428[0m                           <p className="text-sm">Account: {vendor.bankInfo.accountNumber}</p>
[7m   [0m [91m                                                                  ~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m429[0m:[93m67[0m - [91merror[0m[90m TS2339: [0mProperty 'bankInfo' does not exist on type '{ id: string; name: string; email: string; phone: string; totalSpent: number; lastInvoice: string; status: string; taxId: string; paymentTerms: string; address: { street: string; city: string; state: string; zipCode: string; country: string; }; ... 7 more ...; invoiceHistory: { ...; }[]; }'.

[7m429[0m                           <p className="text-sm">Routing: {vendor.bankInfo.routingNumber}</p>
[7m   [0m [91m                                                                  ~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m430[0m:[93m64[0m - [91merror[0m[90m TS2339: [0mProperty 'bankInfo' does not exist on type '{ id: string; name: string; email: string; phone: string; totalSpent: number; lastInvoice: string; status: string; taxId: string; paymentTerms: string; address: { street: string; city: string; state: string; zipCode: string; country: string; }; ... 7 more ...; invoiceHistory: { ...; }[]; }'.

[7m430[0m                           <p className="text-sm">Bank: {vendor.bankInfo.bankName}</p>
[7m   [0m [91m                                                               ~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m531[0m:[93m23[0m - [91merror[0m[90m TS2339: [0mProperty 'complianceDocuments' does not exist on type '{ id: string; name: string; email: string; phone: string; totalSpent: number; lastInvoice: string; status: string; taxId: string; paymentTerms: string; address: { street: string; city: string; state: string; zipCode: string; country: string; }; ... 7 more ...; invoiceHistory: { ...; }[]; }'.

[7m531[0m               {vendor.complianceDocuments ? (
[7m   [0m [91m                      ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m542[0m:[93m29[0m - [91merror[0m[90m TS2339: [0mProperty 'complianceDocuments' does not exist on type '{ id: string; name: string; email: string; phone: string; totalSpent: number; lastInvoice: string; status: string; taxId: string; paymentTerms: string; address: { street: string; city: string; state: string; zipCode: string; country: string; }; ... 7 more ...; invoiceHistory: { ...; }[]; }'.

[7m542[0m                     {vendor.complianceDocuments.map((doc, index) => (
[7m   [0m [91m                            ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m542[0m:[93m54[0m - [91merror[0m[90m TS7006: [0mParameter 'doc' implicitly has an 'any' type.

[7m542[0m                     {vendor.complianceDocuments.map((doc, index) => (
[7m   [0m [91m                                                     ~~~[0m

[96msrc/pages/VendorDetail.tsx[0m:[93m542[0m:[93m59[0m - [91merror[0m[90m TS7006: [0mParameter 'index' implicitly has an 'any' type.

[7m542[0m                     {vendor.complianceDocuments.map((doc, index) => (
[7m   [0m [91m                                                          ~~~~~[0m

[96msrc/service-worker/index.ts[0m:[93m9[0m:[93m34[0m - [91merror[0m[90m TS2307: [0mCannot find module 'workbox-expiration' or its corresponding type declarations.

[7m9[0m import { ExpirationPlugin } from 'workbox-expiration';
[7m [0m [91m                                 ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/service-worker/index.ts[0m:[93m146[0m:[93m5[0m - [91merror[0m[90m TS2552: [0mCannot find name 'clients'. Did you mean 'Clients'?

[7m146[0m     clients.matchAll({ type: 'window' }).then((clientList) => {
[7m   [0m [91m    ~~~~~~~[0m

  [96m../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.webworker.d.ts[0m:[93m1984[0m:[93m13[0m
    [7m1984[0m declare var Clients: {
    [7m    [0m [96m            ~~~~~~~[0m
    'Clients' is declared here.

[96msrc/service-worker/index.ts[0m:[93m146[0m:[93m48[0m - [91merror[0m[90m TS7006: [0mParameter 'clientList' implicitly has an 'any' type.

[7m146[0m     clients.matchAll({ type: 'window' }).then((clientList) => {
[7m   [0m [91m                                               ~~~~~~~~~~[0m

[96msrc/service-worker/index.ts[0m:[93m147[0m:[93m49[0m - [91merror[0m[90m TS7006: [0mParameter 'client' implicitly has an 'any' type.

[7m147[0m       const hadWindowToFocus = clientList.some((client) => {
[7m   [0m [91m                                                ~~~~~~[0m

[96msrc/service-worker/index.ts[0m:[93m156[0m:[93m9[0m - [91merror[0m[90m TS2552: [0mCannot find name 'clients'. Did you mean 'Clients'?

[7m156[0m         clients.openWindow(event.notification.data.url)
[7m   [0m [91m        ~~~~~~~[0m

  [96m../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.webworker.d.ts[0m:[93m1984[0m:[93m13[0m
    [7m1984[0m declare var Clients: {
    [7m    [0m [96m            ~~~~~~~[0m
    'Clients' is declared here.

[96msrc/service-worker/index.ts[0m:[93m157[0m:[93m18[0m - [91merror[0m[90m TS7006: [0mParameter 'windowClient' implicitly has an 'any' type.

[7m157[0m           .then((windowClient) => windowClient && windowClient.focus());
[7m   [0m [91m                 ~~~~~~~~~~~~[0m

[96msrc/service-worker/index.ts[0m:[93m165[0m:[93m13[0m - [91merror[0m[90m TS2339: [0mProperty 'tag' does not exist on type 'Event'.

[7m165[0m   if (event.tag === 'sync-invoices') {
[7m   [0m [91m            ~~~[0m

[96msrc/service-worker/index.ts[0m:[93m166[0m:[93m11[0m - [91merror[0m[90m TS2339: [0mProperty 'waitUntil' does not exist on type 'Event'.

[7m166[0m     event.waitUntil(syncInvoices());
[7m   [0m [91m          ~~~~~~~~~[0m

[96msrc/service-worker/index.ts[0m:[93m176[0m:[93m52[0m - [91merror[0m[90m TS2554: [0mExpected 0 arguments, but got 1.

[7m176[0m     const offlineInvoices = await dbPromise.getAll('offlineInvoices');
[7m   [0m [91m                                                   ~~~~~~~~~~~~~~~~~[0m

[96msrc/service-worker/index.ts[0m:[93m192[0m:[93m34[0m - [91merror[0m[90m TS2554: [0mExpected 0 arguments, but got 2.

[7m192[0m           await dbPromise.delete('offlineInvoices', invoice.id);
[7m   [0m [91m                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/service-worker/index.ts[0m:[93m192[0m:[93m61[0m - [91merror[0m[90m TS2339: [0mProperty 'id' does not exist on type 'never'.

[7m192[0m           await dbPromise.delete('offlineInvoices', invoice.id);
[7m   [0m [91m                                                            ~~[0m

[96msrc/service-worker/register.ts[0m:[93m116[0m:[93m26[0m - [91merror[0m[90m TS2339: [0mProperty 'sync' does not exist on type 'ServiceWorkerRegistration'.

[7m116[0m       await registration.sync.register(tag);
[7m   [0m [91m                         ~~~~[0m

[96msrc/service-worker/sw.ts[0m:[93m6[0m:[93m34[0m - [91merror[0m[90m TS2307: [0mCannot find module 'workbox-expiration' or its corresponding type declarations.

[7m6[0m import { ExpirationPlugin } from 'workbox-expiration';
[7m [0m [91m                                 ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/service-worker/sw.ts[0m:[93m7[0m:[93m41[0m - [91merror[0m[90m TS2307: [0mCannot find module 'workbox-cacheable-response' or its corresponding type declarations.

[7m7[0m import { CacheableResponsePlugin } from 'workbox-cacheable-response';
[7m [0m [91m                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/service-worker/sw.ts[0m:[93m89[0m:[93m13[0m - [91merror[0m[90m TS2339: [0mProperty 'tag' does not exist on type 'Event'.

[7m89[0m   if (event.tag === 'sync-invoices') {
[7m  [0m [91m            ~~~[0m

[96msrc/service-worker/sw.ts[0m:[93m90[0m:[93m11[0m - [91merror[0m[90m TS2339: [0mProperty 'waitUntil' does not exist on type 'Event'.

[7m90[0m     event.waitUntil(syncInvoices());
[7m  [0m [91m          ~~~~~~~~~[0m

[96msrc/service-worker/sw.ts[0m:[93m91[0m:[93m20[0m - [91merror[0m[90m TS2339: [0mProperty 'tag' does not exist on type 'Event'.

[7m91[0m   } else if (event.tag === 'sync-vendors') {
[7m  [0m [91m                   ~~~[0m

[96msrc/service-worker/sw.ts[0m:[93m92[0m:[93m11[0m - [91merror[0m[90m TS2339: [0mProperty 'waitUntil' does not exist on type 'Event'.

[7m92[0m     event.waitUntil(syncVendors());
[7m  [0m [91m          ~~~~~~~~~[0m

[96msrc/service-worker/sw.ts[0m:[93m93[0m:[93m20[0m - [91merror[0m[90m TS2339: [0mProperty 'tag' does not exist on type 'Event'.

[7m93[0m   } else if (event.tag === 'sync-uploads') {
[7m  [0m [91m                   ~~~[0m

[96msrc/service-worker/sw.ts[0m:[93m94[0m:[93m11[0m - [91merror[0m[90m TS2339: [0mProperty 'waitUntil' does not exist on type 'Event'.

[7m94[0m     event.waitUntil(syncUploads());
[7m  [0m [91m          ~~~~~~~~~[0m

[96msrc/service-worker/sw.ts[0m:[93m162[0m:[93m54[0m - [91merror[0m[90m TS7030: [0mNot all code paths return a value.

[7m162[0m       self.clients.matchAll({ type: 'window' }).then((clientList) => {
[7m   [0m [91m                                                     ~~~~~~~~~~~~~~~~~[0m

[96msrc/services/sync-service.ts[0m:[93m4[0m:[93m26[0m - [91merror[0m[90m TS2307: [0mCannot find module '../hooks/use-toast' or its corresponding type declarations.

[7m4[0m import { useToast } from '../hooks/use-toast';
[7m [0m [91m                         ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/sync-service.ts[0m:[93m92[0m:[93m13[0m - [91merror[0m[90m TS2375: [0mType '{ createdAt: Date; fileData: Blob | null; fileUrl: string | null; fileName: string; fileType: string; uploadStatus: "pending"; processingStatus: "pending"; notes: string | undefined; vendor: string | undefined; }' is not assignable to type 'Omit<OfflineInvoice, "id" | "processed" | "syncAttempts">' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'notes' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.

[7m92[0m       const offlineInvoice: Omit<OfflineInvoice, 'id' | 'syncAttempts' | 'processed'> = {
[7m  [0m [91m            ~~~~~~~~~~~~~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m2[0m:[93m27[0m - [91merror[0m[90m TS2305: [0mModule '"@billsnapp/shared-types"' has no exported member 'DEFAULT_USER_PREFERENCES'.

[7m2[0m import { UserPreferences, DEFAULT_USER_PREFERENCES } from '@billsnapp/shared-types';
[7m [0m [91m                          ~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m4[0m:[93m22[0m - [91merror[0m[90m TS2307: [0mCannot find module '@/lib/firebase' or its corresponding type declarations.

[7m4[0m import { auth } from '@/lib/firebase';
[7m [0m [91m                     ~~~~~~~~~~~~~~~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m67[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'dashboard' does not exist in type 'UserPreferences'.

[7m67[0m       dashboard: {
[7m  [0m [91m      ~~~~~~~~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m68[0m:[93m31[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m68[0m         ...currentPreferences.dashboard,
[7m  [0m [91m                              ~~~~~~~~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m69[0m:[93m25[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'Partial<UserPreferences>'.

[7m69[0m         ...(preferences.dashboard || {}),
[7m  [0m [91m                        ~~~~~~~~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m72[0m:[93m33[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m72[0m           ...currentPreferences.dashboard.filters,
[7m  [0m [91m                                ~~~~~~~~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m73[0m:[93m27[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'Partial<UserPreferences>'.

[7m73[0m           ...(preferences.dashboard?.filters || {})
[7m  [0m [91m                          ~~~~~~~~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m81[0m:[93m31[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m81[0m         ...currentPreferences.ui,
[7m  [0m [91m                              ~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m82[0m:[93m25[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'Partial<UserPreferences>'.

[7m82[0m         ...(preferences.ui || {})
[7m  [0m [91m                        ~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m146[0m:[93m58[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m146[0m   public getUiPreference<K extends keyof UserPreferences['ui']>(
[7m   [0m [91m                                                         ~~~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m148[0m:[93m22[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m148[0m   ): UserPreferences['ui'][K] {
[7m   [0m [91m                     ~~~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m154[0m:[93m68[0m - [91merror[0m[90m TS2731: [0mImplicit conversion of a 'symbol' to a 'string' will fail at runtime. Consider wrapping this expression in 'String(...)'.

[7m154[0m     const value = localStorage.getItem(`${this.STORAGE_PREFIX}ui-${key}-${user.uid}`);
[7m   [0m [91m                                                                   ~~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m201[0m:[93m32[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m201[0m     Object.entries(preferences.ui).forEach(([key, value]) => {
[7m   [0m [91m                               ~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m211[0m:[93m19[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m211[0m       preferences.dashboard.defaultView
[7m   [0m [91m                  ~~~~~~~~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m234[0m:[93m26[0m - [91merror[0m[90m TS2339: [0mProperty 'ui' does not exist on type 'UserPreferences'.

[7m234[0m             (preferences.ui as any)[key] = JSON.parse(storedValue);
[7m   [0m [91m                         ~~[0m

[96msrc/services/userPreferencesService.ts[0m:[93m244[0m:[93m21[0m - [91merror[0m[90m TS2339: [0mProperty 'dashboard' does not exist on type 'UserPreferences'.

[7m244[0m         preferences.dashboard.defaultView = dashboardView as 'grid' | 'list' | 'kanban';
[7m   [0m [91m                    ~~~~~~~~~[0m


Found 160 errors in 28 files.

Errors  Files
     5  src/components/InvoiceCapture/CameraCapture.tsx[90m:108[0m
     5  src/components/invoices/InvoiceDetailDrawer.tsx[90m:133[0m
     1  src/components/invoices/InvoiceUpload.tsx[90m:10[0m
     1  src/components/ui/OfflineStatusBar.tsx[90m:19[0m
     2  src/components/ui/sheet.tsx[90m:15[0m
     1  src/components/ui/use-toast.ts[90m:188[0m
     9  src/contexts/AuthContext.tsx[90m:31[0m
     2  src/contexts/TenantContext.tsx[90m:31[0m
    19  src/contexts/UserPreferencesContext.tsx[90m:10[0m
     1  src/hooks/useImageQualityCheck.ts[90m:186[0m
     7  src/lib/api/apiClient.ts[90m:15[0m
     1  src/lib/db.ts[90m:245[0m
     2  src/lib/logger.ts[90m:13[0m
     7  src/lib/trpc.tsx[90m:6[0m
     1  src/main.tsx[90m:4[0m
     1  src/pages/admin/AdminPanel.tsx[90m:9[0m
     1  src/pages/admin/TenantManagement.tsx[90m:9[0m
     1  src/pages/admin/UserManagement.tsx[90m:9[0m
     1  src/pages/AuditLogs.tsx[90m:21[0m
     2  src/pages/InvoiceCapture.tsx[90m:9[0m
     2  src/pages/InvoiceDetails.tsx[90m:7[0m
    34  src/pages/Settings.tsx[90m:6[0m
    15  src/pages/VendorDetail.tsx[90m:13[0m
    11  src/service-worker/index.ts[90m:9[0m
     1  src/service-worker/register.ts[90m:116[0m
     9  src/service-worker/sw.ts[90m:6[0m
     2  src/services/sync-service.ts[90m:4[0m
    16  src/services/userPreferencesService.ts[90m:2[0m
[41m[30m ELIFECYCLE [39m[49m [31mCommand failed with exit code 2.[39m
