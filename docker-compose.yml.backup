version: '3.8'

services:
  # Backend service
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - MONGO_URI=mongodb://mongo:27017
      - MONGO_DB_NAME=aiclearbill
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - FIREBASE_PROJECT_ID=test-project
      - FIREBASE_STORAGE_BUCKET=test-project.appspot.com
      - ENVIRONMENT=development
    env_file:
      - .env
    depends_on:
      - mongo
      - redis
    networks:
      - app-network
    restart: unless-stopped

  # Frontend service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "8080:80"
    volumes:
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - app-network
    restart: unless-stopped

  # MongoDB service
  mongo:
    image: mongo:4.4
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - app-network
    restart: unless-stopped

  # Redis service
  redis:
    image: redis:6
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - app-network
    restart: unless-stopped

  # Celery worker
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile
    command: celery -A backend.workers.celery_app worker --loglevel=info
    environment:
      - MONGO_URI=mongodb://mongo:27017
      - MONGO_DB_NAME=aiclearbill
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - ENVIRONMENT=development
    env_file:
      - .env
    depends_on:
      - mongo
      - redis
      - backend
    networks:
      - app-network
    restart: unless-stopped

networks:
  app-network:
    driver: bridge

volumes:
  mongo-data:
  redis-data:
