# =============================
# Backend Environment Variables
# =============================

# Flask
FLASK_ENV=development
FLASK_DEBUG=1
SECRET_KEY=your-secret-key

# Gemini
GEMINI_API_KEY=AIzaSyDdGCvkmah6c6E2dqgV4Qu_hWR002kd71Y
GEMINI_MODEL="gemini-2.0-flash-001"


# Firebase Admin SDK (Backend)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-firebase-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-firebase-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>
FIREBASE_DATABASE_URL=https://your-firebase-project-id.firebaseio.com
FIREBASE_STORAGE_BUCKET=your-firebase-project-id.appspot.com
# Path to service account file (optional if using above vars)
FIREBASE_SERVICE_ACCOUNT_PATH=backend/firebase-service-account.json

# =============================
# Frontend Environment Variables
# =============================

# Firebase JS SDK (Frontend)
NEXT_PUBLIC_FIREBASE_API_KEY=your-frontend-firebase-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-firebase-project-id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-firebase-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-firebase-project-id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id

# API URL (Frontend -> Backend)
NEXT_PUBLIC_API_URL=http://localhost:5000

# =============================
# Other Optional/3rd Party Vars
# =============================
# (Add as needed for integrations)
# QBO_CLIENT_ID=your-qbo-client-id
# QBO_CLIENT_SECRET=your-qbo-client-secret
# SENTRY_DSN=your-sentry-dsn
