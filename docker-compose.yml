version: '3.8'
services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
    - 8000:8000
    environment:
    - MONGO_URI=mongodb://mongo:27017
    - MONGO_DB_NAME=aiclearbill
    - FIREBASE_PROJECT_ID=test-project
    - FIREBASE_STORAGE_BUCKET=test-project.appspot.com
    - ENVIRONMENT=development
    env_file:
    - .env
    depends_on:
    - mongo
    networks:
    - app-network
    restart: unless-stopped
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
    - 8080:80
    volumes:
    - /app/node_modules
    depends_on:
    - backend
    networks:
    - app-network
    restart: unless-stopped
  mongo:
    image: mongo:4.4
    ports:
    - 27017:27017
    volumes:
    - mongo-data:/data/db
    networks:
    - app-network
    restart: unless-stopped
networks:
  app-network:
    driver: bridge
volumes:
  mongo-data: null
