apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: billsnappurl
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "100"
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/service-account: <EMAIL>
    spec:
      containerConcurrency: 80
      timeoutSeconds: 300
      containers:
      - image: us-central1-docker.pkg.dev/billsnapp/api/api:api-v2
        ports:
        - name: http1
          containerPort: 8080
        env:
        - name: PORT
          value: "8080"
        - name: NODE_ENV
          value: "production"
        - name: FIREBASE_PROJECT_ID
          value: "billsnapp-b3bc2"
        - name: FIREBASE_CLIENT_EMAIL
          value: "<EMAIL>"
        - name: FIREBASE_STORAGE_BUCKET
          value: "billsnapp-b3bc2.appspot.com"
        - name: FIREBASE_PRIVATE_KEY_ID
          value: "4dd5cd1a9b8e4c8f9a2b3c4d5e6f7a8b9c0d1e2f"
        - name: FIREBASE_CLIENT_ID
          value: "117234567890123456789"
        - name: FIREBASE_PRIVATE_KEY
          value: |
            -----BEGIN PRIVATE KEY-----
            MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDLSnHEUuuzX/94
            WfDGGuX9jJnXRDW/UFhoNmhWZDkv9VhNTFBynY0sXFwGVIXApgj2rRqH/Y5sVKwP
            EBecof/8V9Rhmr9UZVmj/DMRG1+BSPcFWx7Dos6rTdiZP0AqoLbCP3wE9RsRu6+S
            bv0k5q+mU6DeXwvadHWX5CQ552x72zSJEins0jrAKu08OyEqIS8JUVpUe23pMi7Z
            j/6GG46oOZSBbHslJO9DHaxrOUlmx5R60yXX7+/OgomUTL5PibMm6Mu6OgQa7l9Z
            d2iuo6ej1IeGhUETph5uZdxWo5Y6oi2R2n5D0VBOa0YVjpkUgCvLUPCEQNX1S2Xl
            BckdNlpAgMBAAECggEAEdee1JRSi5PJYE7Eix6hQPeuKO6bqQbsO3lK/woc2tC1
            x1MDXIvncBVLHmMcuiPYdY4L3hOrBGcKEuheqiitxwAOa+KhGE8s5u+jq8SzRS2a
            QKYWtST87pwOfqTGhPdGExZvVCAPGCdY6YlEPpDgqcB9RgchizLdf09OvqZJJzWV
            +6Vef6xV5mj16O2niLmbHlOzPTpHeo9vNbHqOpvE8wxH/t60dvXZBwyUWDZC43SS
            DJEMy8NMuiidCdm3n5tghBtbHo1DoBbh9sFXfNWy6J6BXX/5EdKSAMLzkZD2hlK6
            Jza6qUugyhxtfZqEG7JagTPd48FQP4ATNuyOmehP4QKBgQDtH2GnDALRgF0f1gEB
            AmZuFPtyDoxc7/3QrbWHwn3ot30fVgRm0OxFKorLHc61cFQjIZbftADJISYpidVG
            PdwrO4+X/9r8jXT6rDn+Knu5oG8g5tzFd4ilXeYjmacCVKc0PlcsWaT7HQtdxIsK
            AdJC9QIN2zhHK6q2dB7qG2AiMwKBgQDbeZAD5mF/6gvrOTTDp4AwwdQ8QnvaSEvJ
            04C+IP1yAnvgjnJBprjXzvpW8RVYEeQQzvM5G72jaGfN82D45nZWc4sVYPxOhj3X
            bhJph3xTNNL52aJ+YNlMDck7NSE/MLH2ZDVC9aSVvRHzQ3vT3sO1aFx0qFtW2f3Z
            f925VjkR8wKBgAm0MRDfAvaD5Eu7PEtIR9zVavzd74yWHbL+iOFHalOtvqcbpA55
            GlIyTX/GdoEiWa8eXshE24tfmeo1Q88eFPaH93Zm+BVBzLm9dpoaLvoHwMR97ib+
            n071KsfmL9l+kWEn8/VSrdfA9ogd1PrR3EL1N+JNSF5RQSk6U1GNUT/VAoGAY1Zn
            P8yjrsDheisfXbtPw0HH7SpuYoM8gnJPoD7QWC0VorL2tg5fNZbCKl3eOkmGv7Db
            16pdsDuOWGJjLuUpBMIxXpDSQjxyezKf9NHVDNgo8dt2t9rgH5NR1JGcpbxFoG8E
            yHTkdL3qBQAzaNc4H8DHsCmPhgUO58X/Nmb3V88CgYEA7DFAvhGTpSRQKJjfMq6a
            pesEBXnsCS826hkr+/vhzIM7xra6yZYbw8a2BlllZbJ5Qo97pOtyLbaiU8bHSs/h
            OxYh1XajZzqiRHTMxAtx5s6kQRE250vGq0G7LqzP4BM8qtLlQpo0w8/TFuIextaQ
            lelXmz9/t5yEl+1dFf8C+xU=
            -----END PRIVATE KEY-----
        resources:
          limits:
            cpu: 1000m
            memory: 512Mi
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 0
          timeoutSeconds: 240
          periodSeconds: 240
          failureThreshold: 1
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 0
          timeoutSeconds: 240
          periodSeconds: 240
          failureThreshold: 1
