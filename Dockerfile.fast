# Fast Docker build using pre-built files
FROM node:18-slim

# Install minimal dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy only the built API
COPY apps/api/dist ./apps/api/dist
COPY apps/api/package.json ./apps/api/package.json

# Copy built shared-types
COPY packages/shared-types/dist ./packages/shared-types/dist
COPY packages/shared-types/package.json ./packages/shared-types/package.json

# Copy workspace configuration and lockfile
COPY pnpm-workspace.yaml package.json pnpm-lock.yaml ./

# Install pnpm
RUN npm install -g pnpm@latest

# Install only production dependencies for the workspace
RUN pnpm install --prod --force

# Set working directory to API
WORKDIR /app/apps/api

# Set environment variables for Cloud Run
ENV NODE_ENV=production
ENV PORT=8080
ENV HOST=0.0.0.0

# Expose the port
EXPOSE 8080

# Run the API server
CMD ["node", "dist/index.js"]
