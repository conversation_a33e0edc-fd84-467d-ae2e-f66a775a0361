name: M2-04 Synthetic Monitoring

on:
  schedule:
    # Run every minute during the 48-hour monitoring window
    - cron: '* * * * *'
  workflow_dispatch:
    inputs:
      duration_hours:
        description: 'Monitoring duration in hours (default: 48)'
        required: false
        default: '48'
      api_url:
        description: 'API URL to monitor (default: production)'
        required: false
        default: 'https://billsnapp-api-[PROJECT_ID].a.run.app'

env:
  MONITORING_START_TIME: ${{ secrets.M2_04_START_TIME }}
  DURATION_HOURS: ${{ github.event.inputs.duration_hours || '48' }}
  API_URL: ${{ github.event.inputs.api_url || secrets.PRODUCTION_API_URL }}
  SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}

jobs:
  synthetic-smoke-test:
    name: Synthetic Smoke Test
    runs-on: ubuntu-latest
    timeout-minutes: 5
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}
      
      - name: Check monitoring window status
        id: check_window
        run: |
          # Check if monitoring window has started
          if [ -z "$MONITORING_START_TIME" ]; then
            echo "🚀 Starting new 48-hour monitoring window"
            START_TIME=$(date -u +%s)
            echo "start_time=$START_TIME" >> $GITHUB_OUTPUT
            echo "window_active=true" >> $GITHUB_OUTPUT
            echo "hours_elapsed=0" >> $GITHUB_OUTPUT
          else
            START_TIME=$MONITORING_START_TIME
            CURRENT_TIME=$(date -u +%s)
            ELAPSED_SECONDS=$((CURRENT_TIME - START_TIME))
            HOURS_ELAPSED=$((ELAPSED_SECONDS / 3600))
            DURATION_HOURS_INT=${{ env.DURATION_HOURS }}
            
            echo "start_time=$START_TIME" >> $GITHUB_OUTPUT
            echo "hours_elapsed=$HOURS_ELAPSED" >> $GITHUB_OUTPUT
            
            if [ $HOURS_ELAPSED -ge $DURATION_HOURS_INT ]; then
              echo "✅ 48-hour monitoring window completed successfully!"
              echo "window_active=false" >> $GITHUB_OUTPUT
              echo "window_completed=true" >> $GITHUB_OUTPUT
            else
              echo "⏱️  Monitoring window active: $HOURS_ELAPSED/$DURATION_HOURS_INT hours elapsed"
              echo "window_active=true" >> $GITHUB_OUTPUT
              echo "window_completed=false" >> $GITHUB_OUTPUT
            fi
          fi
      
      - name: Run synthetic health check
        if: steps.check_window.outputs.window_active == 'true'
        id: health_check
        run: |
          echo "🏥 Testing health endpoint..."
          
          # Test health endpoint with timeout and latency measurement
          START_TIME=$(date +%s%3N)
          
          RESPONSE=$(curl -s -w "%{http_code},%{time_total}" \
            --max-time 3 \
            --connect-timeout 2 \
            "${{ env.API_URL }}/health" || echo "000,999")
          
          END_TIME=$(date +%s%3N)
          
          HTTP_CODE=$(echo $RESPONSE | cut -d',' -f1)
          CURL_TIME=$(echo $RESPONSE | cut -d',' -f2)
          LATENCY_MS=$(echo "scale=0; $CURL_TIME * 1000" | bc)
          
          echo "HTTP Code: $HTTP_CODE"
          echo "Latency: ${LATENCY_MS}ms"
          
          # Check for errors
          if [ "$HTTP_CODE" -ge 500 ]; then
            echo "❌ Health check failed: HTTP $HTTP_CODE"
            echo "health_status=FAILED" >> $GITHUB_OUTPUT
            echo "health_error=HTTP_$HTTP_CODE" >> $GITHUB_OUTPUT
            exit 1
          elif [ "$HTTP_CODE" != "200" ]; then
            echo "⚠️  Health check warning: HTTP $HTTP_CODE"
            echo "health_status=WARNING" >> $GITHUB_OUTPUT
            echo "health_error=HTTP_$HTTP_CODE" >> $GITHUB_OUTPUT
          elif (( $(echo "$LATENCY_MS > 2000" | bc -l) )); then
            echo "⚠️  Health check slow: ${LATENCY_MS}ms > 2000ms"
            echo "health_status=SLOW" >> $GITHUB_OUTPUT
            echo "health_error=LATENCY_${LATENCY_MS}ms" >> $GITHUB_OUTPUT
          else
            echo "✅ Health check passed: HTTP $HTTP_CODE, ${LATENCY_MS}ms"
            echo "health_status=OK" >> $GITHUB_OUTPUT
          fi
          
          echo "health_latency_ms=$LATENCY_MS" >> $GITHUB_OUTPUT
          echo "health_http_code=$HTTP_CODE" >> $GITHUB_OUTPUT
      
      - name: Run synthetic OCR test
        if: steps.check_window.outputs.window_active == 'true'
        id: ocr_test
        run: |
          echo "🔍 Testing OCR endpoint with dummy payload..."
          
          # Create dummy OCR request payload
          DUMMY_PAYLOAD='{
            "invoiceId": "synthetic-test-'$(date +%s)'",
            "tenantId": "synthetic-tenant",
            "imageUrl": "https://storage.googleapis.com/billsnapp-test-images/synthetic-invoice.jpg",
            "originalFilename": "synthetic-test.jpg",
            "startingTier": "TIER_1"
          }'
          
          # Test OCR endpoint with timeout and latency measurement
          START_TIME=$(date +%s%3N)
          
          # Note: This will likely fail due to auth, but we're testing infrastructure
          RESPONSE=$(curl -s -w "%{http_code},%{time_total}" \
            --max-time 5 \
            --connect-timeout 2 \
            -X POST \
            -H "Content-Type: application/json" \
            -d "$DUMMY_PAYLOAD" \
            "${{ env.API_URL }}/trpc/ocr.startOcrJob" || echo "000,999")
          
          END_TIME=$(date +%s%3N)
          
          HTTP_CODE=$(echo $RESPONSE | cut -d',' -f1)
          CURL_TIME=$(echo $RESPONSE | cut -d',' -f2)
          LATENCY_MS=$(echo "scale=0; $CURL_TIME * 1000" | bc)
          
          echo "HTTP Code: $HTTP_CODE"
          echo "Latency: ${LATENCY_MS}ms"
          
          # Check for errors (401 is expected due to no auth, but 500+ indicates infrastructure issues)
          if [ "$HTTP_CODE" -ge 500 ]; then
            echo "❌ OCR test failed: HTTP $HTTP_CODE"
            echo "ocr_status=FAILED" >> $GITHUB_OUTPUT
            echo "ocr_error=HTTP_$HTTP_CODE" >> $GITHUB_OUTPUT
            exit 1
          elif (( $(echo "$LATENCY_MS > 2000" | bc -l) )); then
            echo "⚠️  OCR test slow: ${LATENCY_MS}ms > 2000ms"
            echo "ocr_status=SLOW" >> $GITHUB_OUTPUT
            echo "ocr_error=LATENCY_${LATENCY_MS}ms" >> $GITHUB_OUTPUT
          else
            echo "✅ OCR test passed: HTTP $HTTP_CODE, ${LATENCY_MS}ms"
            echo "ocr_status=OK" >> $GITHUB_OUTPUT
          fi
          
          echo "ocr_latency_ms=$LATENCY_MS" >> $GITHUB_OUTPUT
          echo "ocr_http_code=$HTTP_CODE" >> $GITHUB_OUTPUT
      
      - name: Log results to Cloud Logging
        if: steps.check_window.outputs.window_active == 'true'
        run: |
          # Create structured log entry
          LOG_ENTRY='{
            "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'",
            "severity": "INFO",
            "labels": {
              "component": "synthetic-monitoring",
              "milestone": "M2-04",
              "monitoring_window_hours": "${{ steps.check_window.outputs.hours_elapsed }}"
            },
            "jsonPayload": {
              "health_check": {
                "status": "${{ steps.health_check.outputs.health_status }}",
                "http_code": "${{ steps.health_check.outputs.health_http_code }}",
                "latency_ms": "${{ steps.health_check.outputs.health_latency_ms }}",
                "error": "${{ steps.health_check.outputs.health_error }}"
              },
              "ocr_test": {
                "status": "${{ steps.ocr_test.outputs.ocr_status }}",
                "http_code": "${{ steps.ocr_test.outputs.ocr_http_code }}",
                "latency_ms": "${{ steps.ocr_test.outputs.ocr_latency_ms }}",
                "error": "${{ steps.ocr_test.outputs.ocr_error }}"
              }
            }
          }'
          
          # Write to Cloud Logging
          echo "$LOG_ENTRY" | gcloud logging write synthetic-monitoring-m2-04 --severity=INFO
          
          echo "📊 Results logged to Cloud Logging"
      
      - name: Update monitoring start time secret
        if: steps.check_window.outputs.start_time != env.MONITORING_START_TIME
        run: |
          echo "🔄 Updating monitoring start time secret..."
          # This would typically be done via GitHub API or repository secrets
          # For now, we'll log the start time for manual update
          echo "Please update M2_04_START_TIME secret to: ${{ steps.check_window.outputs.start_time }}"
      
      - name: Send completion notification
        if: steps.check_window.outputs.window_completed == 'true'
        run: |
          echo "🎉 48-hour monitoring window completed successfully!"
          
          # Send Slack notification
          if [ -n "$SLACK_WEBHOOK_URL" ]; then
            curl -X POST -H 'Content-type: application/json' \
              --data '{
                "text": "🎉 M2-04 Synthetic Monitoring Complete!",
                "blocks": [
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*M2-04 48-Hour Monitoring Window Complete* ✅\n\nThe TypeScript backend has run error-free for 48 hours under continuous synthetic load.\n\n*Next Steps:*\n• Proceed to M2-05 (Final Python cleanup)\n• Review monitoring logs in Cloud Logging\n• Celebrate the successful migration! 🚀"
                    }
                  }
                ]
              }' \
              "$SLACK_WEBHOOK_URL"
          fi
          
          # Disable the cron schedule by updating workflow (manual step required)
          echo "⚠️  Manual action required: Disable the cron schedule in this workflow"
          echo "The 48-hour window is complete. Please update the workflow to prevent further runs."
      
      - name: Handle test failures
        if: failure()
        run: |
          echo "❌ Synthetic monitoring test failed"
          
          # Send failure notification
          if [ -n "$SLACK_WEBHOOK_URL" ]; then
            curl -X POST -H 'Content-type: application/json' \
              --data '{
                "text": "🚨 M2-04 Synthetic Monitoring Alert",
                "blocks": [
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*M2-04 Monitoring Alert* 🚨\n\nSynthetic monitoring detected an issue with the TypeScript backend.\n\n*Health Check:* ${{ steps.health_check.outputs.health_status || 'UNKNOWN' }}\n*OCR Test:* ${{ steps.ocr_test.outputs.ocr_status || 'UNKNOWN' }}\n\nPlease investigate the issue before proceeding to M2-05."
                    }
                  }
                ]
              }' \
              "$SLACK_WEBHOOK_URL"
          fi
          
          exit 1
